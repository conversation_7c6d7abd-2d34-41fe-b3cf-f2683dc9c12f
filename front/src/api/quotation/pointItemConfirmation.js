import request from '@/utils/request'

// 根据项目报价ID获取点位明细列表
export function getPointItemsByQuotationId(projectQuotationId, pointConfirmed = null) {
  return request({
    url: `/quotation/point-item-confirmation/project/${projectQuotationId}`,
    method: 'get',
    params: {
      pointConfirmed: pointConfirmed
    }
  })
}

// 根据ID获取点位明细
export function getPointItemById(pointItemId) {
  return request({
    url: `/quotation/point-item-confirmation/${pointItemId}`,
    method: 'get'
  })
}

// 重命名点位
export function renamePointItem(data) {
  return request({
    url: '/quotation/point-item-confirmation/rename',
    method: 'put',
    data: data
  })
}

// 确认/取消确认点位
export function confirmPointItem(data) {
  return request({
    url: '/quotation/point-item-confirmation/confirm',
    method: 'put',
    data: data
  })
}

// 批量确认/取消确认点位
export function batchConfirmPointItems(data) {
  return request({
    url: '/quotation/point-item-confirmation/batch-confirm',
    method: 'put',
    data: data
  })
}

// 获取合并后的点位列表（按点位名称合并）
export function getMergedPointItemsByQuotationId(projectQuotationId) {
  return request({
    url: `/quotation/point-item-confirmation/project/${projectQuotationId}/merged`,
    method: 'get'
  })
}

// 按点位名称批量确认/取消确认点位
export function batchConfirmPointItemsByName(data) {
  return request({
    url: '/quotation/point-item-confirmation/batch-confirm-by-name',
    method: 'put',
    data: data
  })
}

// 按点位名称批量重命名点位
export function batchRenamePointItemsByName(data) {
  return request({
    url: '/quotation/point-item-confirmation/batch-rename-by-name',
    method: 'put',
    data: data
  })
}
