import request from '@/utils/request'

// 查询已审批项目报价分页列表
export function getApprovedQuotationsPage(query) {
  return request({
    url: '/sampling/task-creation/approved-quotations/page',
    method: 'get',
    params: query
  })
}

// 获取项目报价的已确认点位列表
export function getConfirmedPointItems(quotationId) {
  return request({
    url: `/sampling/task-creation/quotation/${quotationId}/confirmed-points`,
    method: 'get'
  })
}

// 根据选定点位获取检测周期条目
export function getCycleItemsByPointItems(pointItemIds) {
  return request({
    url: '/sampling/task-creation/cycle-items-by-points',
    method: 'post',
    data: pointItemIds
  })
}

// 获取项目报价的检测周期条目（原有方法，保持兼容性）
export function getQuotationCycleItems(quotationId) {
  return request({
    url: `/sampling/task-creation/quotation/${quotationId}/cycle-items`,
    method: 'get'
  })
}

// 创建采样任务
export function createSamplingTask(data) {
  return request({
    url: '/sampling/task-creation/create-task',
    method: 'post',
    data: data
  })
}