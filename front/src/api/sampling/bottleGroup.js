import request from '@/utils/request'

// 为任务生成瓶组
export function generateBottleGroups(taskId) {
  return request({
    url: `/sampling/bottle-groups/generate/${taskId}`,
    method: 'post',
    timeout: 30000  // 增加超时时间到30秒
  })
}

// 获取任务的瓶组列表
export function getBottleGroupsByTask(taskId) {
  return request({
    url: `/sampling/bottle-groups/task/${taskId}`,
    method: 'get'
  })
}

// 获取瓶组详情
export function getBottleGroupDetail(bottleGroupId) {
  return request({
    url: `/sampling/bottle-groups/${bottleGroupId}`,
    method: 'get'
  })
}

// 更新瓶组状态
export function updateBottleGroupStatus(bottleGroupId, status) {
  return request({
    url: `/sampling/bottle-groups/${bottleGroupId}/status`,
    method: 'put',
    data: status
  })
}

// 获取瓶组状态选项
export function getBottleGroupStatusOptions(bottleGroupId) {
  return request({
    url: `/sampling/bottle-groups/${bottleGroupId}/status-options`,
    method: 'get'
  })
}

// 删除瓶组
export function deleteBottleGroup(bottleGroupId) {
  return request({
    url: `/sampling/bottle-groups/${bottleGroupId}`,
    method: 'delete'
  })
}

// 获取瓶组关联的样品列表
export function getBottleGroupSamples(bottleGroupId) {
  return request({
    url: `/sampling/bottle-groups/${bottleGroupId}/samples`,
    method: 'get'
  })
}

// 获取样品关联的瓶组列表
export function getBottleGroupsBySample(sampleId) {
  return request({
    url: `/sampling/bottle-groups/sample/${sampleId}`,
    method: 'get'
  })
}

// 批量更新瓶组状态
export function batchUpdateBottleGroupStatus(bottleGroupIds, status) {
  const promises = bottleGroupIds.map(id => updateBottleGroupStatus(id, status))
  return Promise.all(promises)
}

// 获取瓶组状态统计
export function getBottleGroupStatistics(taskId) {
  return getBottleGroupsByTask(taskId).then(response => {
    const bottleGroups = response.data || []
    const statistics = {
      totalCount: bottleGroups.length,
      samplingCount: bottleGroups.filter(group => group.status === 0).length,    // 采样
      packingCount: bottleGroups.filter(group => group.status === 1).length,     // 装箱
      transferCount: bottleGroups.filter(group => group.status === 2).length,    // 流转
      completedCount: bottleGroups.filter(group => group.status === 3).length    // 完成
    }
    return { data: statistics }
  })
}

// 获取瓶组状态文本
export function getBottleGroupStatusText(status) {
  const statusMap = {
    0: '采样',
    1: '装箱',
    2: '流转',
    3: '完成'
  }
  return statusMap[status] || '未知'
}

// 获取瓶组状态类型（用于Element Plus的tag type）
export function getBottleGroupStatusType(status) {
  const typeMap = {
    0: 'info',     // 采样 - 蓝色
    1: 'warning',  // 装箱 - 橙色
    2: 'primary',  // 流转 - 紫色
    3: 'success'   // 完成 - 绿色
  }
  return typeMap[status] || 'info'
}
