<template>
  <el-dialog
    v-model="dialogVisible"
    title="点位确认"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 切换展示模式的按钮 -->
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
        <span>点位确认</span>
        <el-button
          type="primary"
          size="small"
          @click="switchToMergedView"
        >
          切换到合并展示
        </el-button>
      </div>
    </template>
    <div class="point-confirmation-container">
      <!-- 操作按钮区域 -->
      <div class="operation-bar">
        <el-button
          type="primary"
          size="small"
          icon="Check"
          :disabled="selectedPointItems.length === 0"
          @click="handleBatchConfirm()"
        >
          批量确认 ({{ selectedPointItems.length }})
        </el-button>

        <el-button
          type="info"
          size="small"
          icon="Refresh"
          @click="loadPointItems"
        >
          刷新
        </el-button>
      </div>

      <!-- 点位列表 -->
      <el-table
        v-loading="loading"
        :data="pointItems"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="isRowSelectable" />
        <el-table-column label="点位名称" prop="pointName" min-width="150">
          <template #default="{ row }">
            <div class="point-name-cell">
              <span v-if="!row.editing" class="point-name-text">{{ row.pointName }}</span>
              <el-input
                v-else
                v-model="row.editingName"
                size="small"
                @blur="handleBlurRename(row)"
                @keyup.enter="handleEnterRename(row)"
                @keyup.esc="handleCancelRename(row)"
                ref="editInput"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="确认状态" align="center" width="100">
          <template #default="{ row }">
            <el-tag :type="row.pointConfirmed ? 'success' : 'info'" size="small">
              {{ row.pointConfirmed ? '已确认' : '未确认' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="确认人" prop="confirmedBy" align="center" width="100" />
        <el-table-column label="确认时间" align="center" width="150">
          <template #default="{ row }">
            <span>{{ row.confirmedTime ? parseTime(row.confirmedTime, '{y}-{m}-{d} {h}:{i}') : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180">
          <template #default="{ row }">
            <!-- 重命名按钮 - 已确认的点位不能重命名 -->
            <el-button
              v-if="!row.editing && !row.pointConfirmed"
              type="text"
              size="small"
              icon="Edit"
              @click="handleStartRename(row)"
            >
              重命名
            </el-button>
            <!-- 已确认点位显示禁用的重命名按钮 -->
            <el-button
              v-if="!row.editing && row.pointConfirmed"
              type="text"
              size="small"
              icon="Edit"
              disabled
              title="已确认的点位不能重命名"
            >
              重命名
            </el-button>
            <template v-else-if="row.editing">
              <el-button
                type="text"
                size="small"
                icon="Check"
                @click="handleSaveRename(row)"
              >
                保存
              </el-button>
              <el-button
                type="text"
                size="small"
                icon="Close"
                @click="handleCancelRename(row)"
              >
                取消
              </el-button>
            </template>

            <!-- 确认按钮 - 只有未确认的点位才显示 -->
            <el-button
              v-if="!row.pointConfirmed"
              type="text"
              size="small"
              icon="Check"
              @click="handleConfirmWithPrompt(row, true)"
            >
              确认
            </el-button>
            <!-- 已确认的点位显示状态文本 -->
            <span v-else class="confirmed-status">
              已确认
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 统计信息 -->
      <div class="statistics-bar">
        <span>总计：{{ pointItems.length }} 个点位</span>
        <span>已确认：{{ confirmedCount }} 个</span>
        <span>未确认：{{ unconfirmedCount }} 个</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { 
  getPointItemsByQuotationId, 
  renamePointItem, 
  confirmPointItem, 
  batchConfirmPointItems 
} from '@/api/quotation/pointItemConfirmation'

export default {
  name: 'PointItemConfirmation',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectQuotationId: {
      type: Number,
      default: null
    }
  },
  emits: ['update:visible', 'confirmed', 'switch-to-merged'],
  data() {
    return {
      loading: false,
      pointItems: [],
      selectedPointItems: [],
      batchProcessing: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    confirmedCount() {
      return this.pointItems.filter(item => item.pointConfirmed).length
    },
    unconfirmedCount() {
      return this.pointItems.filter(item => !item.pointConfirmed).length
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.projectQuotationId) {
        this.loadPointItems()
      }
    }
  },
  methods: {
    async loadPointItems() {
      if (!this.projectQuotationId) return
      
      try {
        this.loading = true
        const response = await getPointItemsByQuotationId(this.projectQuotationId)
        this.pointItems = response.data.map(item => ({
          ...item,
          editing: false,
          editingName: item.pointName
        }))
      } catch (error) {
        console.error('加载点位列表失败:', error)
        this.$modal.msgError('加载点位列表失败')
      } finally {
        this.loading = false
      }
    },

    handleSelectionChange(selection) {
      this.selectedPointItems = selection
    },

    // 控制行是否可选择 - 已确认的点位在批量取消确认时不可选择
    isRowSelectable(row) {
      // 所有行都可以选择，但在批量操作时会有不同的处理逻辑
      return true
    },

    handleStartRename(row) {
      row.editing = true
      row.editingName = row.pointName
      this.$nextTick(() => {
        // 聚焦到编辑输入框
        const editInputs = this.$refs.editInput
        if (editInputs && editInputs.length) {
          const targetInput = editInputs.find(input => input.$el.closest('tr') === row.$el)
          if (targetInput) {
            targetInput.focus()
          }
        }
      })
    },

    async handleSaveRename(row) {
      // 防止重复提交
      if (row.saving) {
        return
      }

      if (!row.editingName || row.editingName.trim() === '') {
        this.$modal.msgWarning('点位名称不能为空')
        return
      }

      if (row.editingName === row.pointName) {
        row.editing = false
        return
      }

      try {
        row.saving = true
        await renamePointItem({
          pointItemId: row.id,
          newPointName: row.editingName.trim()
        })

        row.pointName = row.editingName.trim()
        row.editing = false
        this.$modal.msgSuccess('点位重命名成功')
      } catch (error) {
        console.error('重命名失败:', error)
        this.$modal.msgError('重命名失败')
      } finally {
        row.saving = false
      }
    },

    handleCancelRename(row) {
      row.editing = false
      row.editingName = row.pointName
    },

    handleEnterRename(row) {
      // 回车键保存，设置标记防止blur事件重复触发
      row.enterPressed = true
      this.handleSaveRename(row)
      setTimeout(() => {
        row.enterPressed = false
      }, 100)
    },

    handleBlurRename(row) {
      // 如果刚刚按了回车键，则不处理blur事件
      if (row.enterPressed) {
        return
      }
      this.handleSaveRename(row)
    },

    // 带确认提示的点位确认方法
    async handleConfirmWithPrompt(row, confirmed) {
      const action = confirmed ? '确认' : '取消确认'
      const message = confirmed
        ? `确认后该点位将不能再修改，是否确认点位"${row.pointName}"？`
        : `是否取消确认点位"${row.pointName}"？`

      try {
        await this.$modal.confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: confirmed ? 'warning' : 'info'
        })

        // 确认后调用实际的确认方法
        await this.handleConfirm(row, confirmed)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认操作失败:', error)
        }
      }
    },

    async handleConfirm(row, confirmed) {
      // 防止重复提交
      if (row.confirming) {
        return
      }

      try {
        row.confirming = true
        await confirmPointItem({
          pointItemId: row.id,
          confirmed: confirmed
        })

        row.pointConfirmed = confirmed
        if (confirmed) {
          // 安全获取用户名
          row.confirmedBy = this.getCurrentUserName()
          row.confirmedTime = new Date()
        } else {
          row.confirmedBy = null
          row.confirmedTime = null
        }

        const action = confirmed ? '确认' : '取消确认'
        this.$modal.msgSuccess(`点位${action}成功`)

        // 触发确认状态变更事件
        this.$emit('confirmed', {
          pointItemId: row.id,
          confirmed: confirmed
        })
      } catch (error) {
        console.error('确认操作失败:', error)
        this.$modal.msgError('确认操作失败')
      } finally {
        row.confirming = false
      }
    },

    async handleBatchConfirm() {
      if (this.selectedPointItems.length === 0) {
        this.$modal.msgWarning('请选择要操作的点位')
        return
      }

      // 过滤掉已确认的点位，只确认未确认的点位
      const unconfirmedItems = this.selectedPointItems.filter(item => !item.pointConfirmed)
      if (unconfirmedItems.length === 0) {
        this.$modal.msgWarning('选中的点位都已确认，无需重复确认')
        return
      }

      // 防止重复提交
      if (this.batchProcessing) {
        return
      }

      const message = `确认后这些点位将不能再修改，是否确认选中的 ${unconfirmedItems.length} 个未确认点位？`

      try {
        await this.$modal.confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.batchProcessing = true
        const pointItemIds = unconfirmedItems.map(item => item.id)
        await batchConfirmPointItems({
          pointItemIds: pointItemIds,
          confirmed: true
        })

        // 更新本地数据 - 只更新未确认的点位
        unconfirmedItems.forEach(selectedItem => {
          const item = this.pointItems.find(p => p.id === selectedItem.id)
          if (item) {
            item.pointConfirmed = true
            item.confirmedBy = this.getCurrentUserName()
            item.confirmedTime = new Date()
          }
        })

        this.$modal.msgSuccess(`批量确认成功，共确认了 ${unconfirmedItems.length} 个点位`)

        // 触发确认状态变更事件
        this.$emit('confirmed', {
          pointItemIds: pointItemIds,
          confirmed: true
        })
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量确认操作失败:', error)
          this.$modal.msgError('批量确认操作失败')
        }
      } finally {
        this.batchProcessing = false
      }
    },

    handleClose() {
      this.dialogVisible = false
      this.selectedPointItems = []
      this.pointItems = []
    },

    getCurrentUserName() {
      // 安全获取当前用户名
      try {
        if (this.$store && this.$store.state && this.$store.state.user && this.$store.state.user.name) {
          return this.$store.state.user.name
        }
      } catch (error) {
        console.warn('获取用户名失败:', error)
      }
      return 'admin' // 默认用户名
    },

    switchToMergedView() {
      this.$emit('switch-to-merged')
    }
  }
}
</script>

<style scoped>
.point-confirmation-container {
  max-height: 600px;
}

.operation-bar {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);
}

.operation-bar .el-button {
  margin-right: 8px;
}

.point-name-cell {
  display: flex;
  align-items: center;
}

.point-name-text {
  flex: 1;
  word-break: break-all;
}

.statistics-bar {
  margin-top: 16px;
  padding: 8px 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.statistics-bar span {
  margin-right: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.dialog-footer {
  text-align: right;
}

.confirmed-status {
  color: var(--el-color-success);
  font-size: 12px;
  font-weight: 500;
}
</style>
