<template>
  <el-dialog
    v-model="dialogVisible"
    title="点位确认（合并展示）"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 切换展示模式的按钮 -->
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
        <span>点位确认（合并展示）</span>
        <el-button
          type="default"
          size="small"
          @click="switchToNormalView"
        >
          切换到详细展示
        </el-button>
      </div>
    </template>
    <div class="merged-point-confirmation-container">
      <!-- 操作按钮区域 -->
      <div class="operation-bar">
        <el-button
          type="primary"
          size="small"
          icon="Check"
          :disabled="selectedMergedItems.length === 0"
          @click="handleBatchConfirmMerged()"
        >
          批量确认 ({{ selectedMergedItems.length }})
        </el-button>

        <el-button
          type="info"
          size="small"
          icon="Refresh"
          @click="loadMergedPointItems"
        >
          刷新
        </el-button>
      </div>

      <!-- 合并点位列表 -->
      <el-table
        v-loading="loading"
        :data="mergedPointItems"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        row-key="pointName"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="isRowSelectable" />
        
        <!-- 点位名称列 -->
        <el-table-column label="点位名称" prop="pointName" min-width="150">
          <template #default="{ row }">
            <div class="point-name-cell">
              <span v-if="!row.editing" class="point-name-text">{{ row.pointName }}</span>
              <el-input
                v-else
                v-model="row.editingName"
                size="small"
                @blur="handleBlurRename(row)"
                @keyup.enter="handleEnterRename(row)"
                @keyup.esc="handleCancelRename(row)"
                ref="editInput"
              />
            </div>
          </template>
        </el-table-column>
        
        <!-- 确认状态列 -->
        <el-table-column label="确认状态" align="center" width="120">
          <template #default="{ row }">
            <el-tag 
              :type="row.allConfirmed ? 'success' : (row.confirmedCount > 0 ? 'warning' : 'info')" 
              size="small"
            >
              {{ row.confirmedCount }}/{{ row.totalCount }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 数量列 -->
        <el-table-column label="明细数量" prop="totalCount" align="center" width="100" />
        
        <!-- 操作列 -->
        <el-table-column label="操作" align="center" width="200">
          <template #default="{ row }">
            <!-- 重命名按钮 -->
            <el-button
              v-if="!row.editing && row.canRename"
              type="text"
              size="small"
              icon="Edit"
              @click="handleStartRename(row)"
            >
              重命名
            </el-button>
            
            <!-- 已确认点位显示禁用的重命名按钮 -->
            <el-button
              v-if="!row.editing && !row.canRename"
              type="text"
              size="small"
              icon="Edit"
              disabled
              title="存在已确认的明细，无法重命名"
            >
              重命名
            </el-button>
            
            <!-- 编辑状态下的保存和取消按钮 -->
            <template v-if="row.editing">
              <el-button
                type="text"
                size="small"
                icon="Check"
                @click="handleSaveRename(row)"
                :loading="row.saving"
              >
                保存
              </el-button>
              <el-button
                type="text"
                size="small"
                icon="Close"
                @click="handleCancelRename(row)"
              >
                取消
              </el-button>
            </template>
            
            <!-- 确认按钮 -->
            <el-button
              v-if="!row.editing && row.hasUnconfirmed"
              type="text"
              size="small"
              icon="Check"
              @click="handleConfirmMerged(row, true)"
              :loading="row.confirming"
            >
              确认
            </el-button>
            
            <!-- 取消确认按钮 -->
            <el-button
              v-if="!row.editing && row.confirmedCount > 0"
              type="text"
              size="small"
              icon="Close"
              @click="handleConfirmMerged(row, false)"
              :loading="row.confirming"
            >
              取消确认
            </el-button>
          </template>
        </el-table-column>
        

      </el-table>
    </div>

    <!-- 底部统计信息 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-stats">
          <span>总计：{{ totalMergedCount }} 个点位，{{ totalItemCount }} 个明细</span>
          <span style="margin-left: 20px;">已确认：{{ confirmedMergedCount }} 个点位</span>
        </div>
        <div>
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import { 
  getMergedPointItemsByQuotationId,
  batchConfirmPointItemsByName,
  batchRenamePointItemsByName
} from '@/api/quotation/pointItemConfirmation'

export default {
  name: 'MergedPointItemConfirmation',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectQuotationId: {
      type: Number,
      default: null
    }
  },
  emits: ['update:visible', 'confirmed', 'switch-to-normal'],
  data() {
    return {
      loading: false,
      mergedPointItems: [],
      selectedMergedItems: [],
      batchProcessing: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    totalMergedCount() {
      return this.mergedPointItems.length
    },
    totalItemCount() {
      return this.mergedPointItems.reduce((sum, item) => sum + item.totalCount, 0)
    },
    confirmedMergedCount() {
      return this.mergedPointItems.filter(item => item.allConfirmed).length
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.projectQuotationId) {
        this.loadMergedPointItems()
      }
    }
  },
  methods: {
    parseTime,
    
    async loadMergedPointItems() {
      if (!this.projectQuotationId) return
      
      try {
        this.loading = true
        const response = await getMergedPointItemsByQuotationId(this.projectQuotationId)
        this.mergedPointItems = response.data.map(item => ({
          ...item,
          editing: false,
          editingName: item.pointName,
          saving: false,
          confirming: false
        }))
      } catch (error) {
        console.error('加载合并点位列表失败:', error)
        this.$modal.msgError('加载合并点位列表失败')
      } finally {
        this.loading = false
      }
    },

    handleSelectionChange(selection) {
      this.selectedMergedItems = selection
    },

    isRowSelectable(row) {
      return true
    },

    handleClose() {
      this.dialogVisible = false
    },

    // 重命名相关方法
    handleStartRename(row) {
      row.editing = true
      row.editingName = row.pointName
      this.$nextTick(() => {
        const editInputs = this.$refs.editInput
        if (editInputs && editInputs.length) {
          const targetInput = editInputs.find(input => input.$el.closest('tr') === row.$el)
          if (targetInput) {
            targetInput.focus()
          }
        }
      })
    },

    async handleSaveRename(row) {
      if (row.saving) return

      if (!row.editingName || row.editingName.trim() === '') {
        this.$modal.msgWarning('点位名称不能为空')
        return
      }

      if (row.editingName === row.pointName) {
        row.editing = false
        return
      }

      try {
        row.saving = true
        await batchRenamePointItemsByName({
          projectQuotationId: this.projectQuotationId,
          oldPointName: row.pointName,
          newPointName: row.editingName.trim()
        })

        row.pointName = row.editingName.trim()
        row.editing = false
        this.$modal.msgSuccess('点位重命名成功')
        
        // 重新加载数据
        await this.loadMergedPointItems()
      } catch (error) {
        console.error('重命名失败:', error)
        this.$modal.msgError('重命名失败')
      } finally {
        row.saving = false
      }
    },

    handleCancelRename(row) {
      row.editing = false
      row.editingName = row.pointName
    },

    handleEnterRename(row) {
      row.enterPressed = true
      this.handleSaveRename(row)
      setTimeout(() => {
        row.enterPressed = false
      }, 100)
    },

    handleBlurRename(row) {
      if (row.enterPressed) return
      this.handleSaveRename(row)
    },

    // 确认相关方法
    async handleConfirmMerged(row, confirmed) {
      if (row.confirming) return

      const action = confirmed ? '确认' : '取消确认'
      const message = confirmed
        ? `确认后该点位的所有明细将不能再修改，是否确认点位"${row.pointName}"的所有明细？`
        : `是否取消确认点位"${row.pointName}"的所有已确认明细？`

      try {
        await this.$modal.confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: confirmed ? 'warning' : 'info'
        })

        row.confirming = true
        await batchConfirmPointItemsByName({
          projectQuotationId: this.projectQuotationId,
          pointName: row.pointName,
          confirmed: confirmed
        })

        this.$modal.msgSuccess(`点位${action}成功`)
        
        // 重新加载数据
        await this.loadMergedPointItems()
        
        // 触发确认状态变更事件
        this.$emit('confirmed', {
          pointName: row.pointName,
          confirmed: confirmed
        })
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认操作失败:', error)
          this.$modal.msgError('确认操作失败')
        }
      } finally {
        row.confirming = false
      }
    },

    async handleBatchConfirmMerged() {
      const unconfirmedItems = this.selectedMergedItems.filter(item => item.hasUnconfirmed)
      
      if (unconfirmedItems.length === 0) {
        this.$modal.msgWarning('请选择有未确认明细的点位')
        return
      }

      const message = `确认后这些点位的所有明细将不能再修改，是否确认选中的 ${unconfirmedItems.length} 个点位的所有明细？`

      try {
        await this.$modal.confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.batchProcessing = true
        
        // 逐个确认点位
        for (const item of unconfirmedItems) {
          await batchConfirmPointItemsByName({
            projectQuotationId: this.projectQuotationId,
            pointName: item.pointName,
            confirmed: true
          })
        }

        this.$modal.msgSuccess(`批量确认成功，共确认了 ${unconfirmedItems.length} 个点位`)
        
        // 重新加载数据
        await this.loadMergedPointItems()
        
        // 触发确认状态变更事件
        this.$emit('confirmed', {
          pointNames: unconfirmedItems.map(item => item.pointName),
          confirmed: true
        })
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量确认失败:', error)
          this.$modal.msgError('批量确认失败')
        }
      } finally {
        this.batchProcessing = false
      }
    },

    switchToNormalView() {
      this.$emit('switch-to-normal')
    }
  }
}
</script>

<style scoped>
.merged-point-confirmation-container {
  max-height: 600px;
  overflow-y: auto;
}

.operation-bar {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.point-name-cell {
  display: flex;
  align-items: center;
}

.point-name-text {
  flex: 1;
}



.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-stats {
  color: #666;
  font-size: 14px;
}
</style>
