<template>
  <div>
    <!-- 普通展示模式 -->
    <PointItemConfirmation
      v-if="!useMergedView"
      :visible="visible"
      :project-quotation-id="projectQuotationId"
      @update:visible="$emit('update:visible', $event)"
      @confirmed="$emit('confirmed', $event)"
      @switch-to-merged="switchToMergedView"
    />
    
    <!-- 合并展示模式 -->
    <MergedPointItemConfirmation
      v-if="useMergedView"
      :visible="visible"
      :project-quotation-id="projectQuotationId"
      @update:visible="$emit('update:visible', $event)"
      @confirmed="$emit('confirmed', $event)"
      @switch-to-normal="switchToNormalView"
    />
  </div>
</template>

<script>
import PointItemConfirmation from './index.vue'
import MergedPointItemConfirmation from './MergedPointItemConfirmation.vue'

export default {
  name: 'PointItemConfirmationWrapper',
  components: {
    PointItemConfirmation,
    MergedPointItemConfirmation
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    projectQuotationId: {
      type: Number,
      default: null
    },
    defaultMergedView: {
      type: Boolean,
      default: true // 默认使用合并展示
    }
  },
  emits: ['update:visible', 'confirmed'],
  data() {
    return {
      useMergedView: this.defaultMergedView
    }
  },
  methods: {
    switchToMergedView() {
      this.useMergedView = true
    },
    
    switchToNormalView() {
      this.useMergedView = false
    }
  }
}
</script>
