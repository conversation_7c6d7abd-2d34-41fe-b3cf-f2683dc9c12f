<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Refresh"
          @click="getList"
        >刷新</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="quotationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectCode" width="120" />
        <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
        <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
        <el-table-column label="客户联系人" align="center" prop="customerContact" width="100" />
        <el-table-column label="客户电话" align="center" prop="customerPhone" width="120" />
        <el-table-column label="委托日期" align="center" prop="commissionDate" width="100">
          <template #default="scope">
            <span>{{ getFormattedCommissionDate(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目经理" align="center" prop="projectManager" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ getFormattedCreateTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template #default="scope">
            <el-button
              type="text"
              icon="Check"
              @click="handlePointConfirmation(scope.row)"
              v-hasPermi="['quotation:point:confirm']"
            >点位确认</el-button>
            <el-button
              type="text"
              icon="Operation"
              @click="handleAssignment(scope.row)"
              v-hasPermi="['sampling:assignment:assign']"
            >采样分配</el-button>
          </template>
        </el-table-column>
      </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 采样任务分配向导 -->
    <SamplingAssignmentWizard
      v-model:visible="assignmentOpen"
      :quotation="selectedQuotation"
      @success="handleAssignmentSuccess"
    />

    <!-- 原有的采样任务分配弹窗（保留作为备用） -->
    <el-dialog title="采样任务分配（原版）" v-model="legacyAssignmentOpen" width="1000px" append-to-body v-if="false">
      <div v-if="selectedQuotation">
        <div class="assignment-header">
          <h3>{{ selectedQuotation.projectName }} ({{ selectedQuotation.projectCode }})</h3>
          <p class="text-muted">客户：{{ selectedQuotation.customerName }}</p>
        </div>

        <el-form :model="assignmentForm" ref="assignmentForm" :rules="assignmentRules" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="任务名称" prop="taskName">
                <el-input v-model="assignmentForm.taskName" placeholder="请输入任务名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人" prop="responsibleUserId">
                <el-select v-model="assignmentForm.responsibleUserId" placeholder="请选择负责人" clearable style="width: 100%">
                  <el-option
                    v-for="user in userList"
                    :key="user.userId"
                    :label="user.nickName"
                    :value="user.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="任务描述" prop="taskDescription">
            <el-input
              v-model="assignmentForm.taskDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入任务描述"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="计划开始时间">
                <el-date-picker
                  v-model="assignmentForm.plannedStartTime"
                  type="datetime"
                  placeholder="选择开始时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计划结束时间">
                <el-date-picker
                  v-model="assignmentForm.plannedEndTime"
                  type="datetime"
                  placeholder="选择结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 检测项目选择区域 -->
        <div class="detection-items-section">
          <div class="section-header">
            <h4>检测项目选择</h4>
            <div class="batch-operation-buttons">
               <el-tooltip 
                 content="纵向选择所有检测项目的下一个可选周期，可多次点击连续选择" 
                 placement="top"
               >
                 <el-button 
                   type="primary" 
                   size="small" 
                   icon="Select" 
                   @click="handleBatchSelectNext"
                   :disabled="filteredCycleItems.length === 0"
                 >
                   一键选择
                 </el-button>
               </el-tooltip>
               <el-tooltip 
                 content="回退上一次一键选择操作，如无历史记录则清空所有选择" 
                 placement="top"
               >
                 <el-button 
                   type="warning" 
                   size="small" 
                   icon="RefreshLeft" 
                   @click="handleBatchCancel"
                   :disabled="selectedCycleItemIds.length === 0"
                 >
                   一键取消
                 </el-button>
               </el-tooltip>
             </div>
          </div>
          <p class="text-muted">说明：周期必须按顺序选择，一个采样任务可关联多个检测项目的不同周期。一键选择会纵向选择所有检测项目的下一个可选周期。</p>
          <p class="text-warning">注意：此处仅显示已确认点位的检测周期条目，如需更多点位请先进行点位确认。</p>
          
          <!-- 检测项目搜索框 -->
          <div class="detection-search-box">
            <el-input
              v-model="detectionSearchKeyword"
              placeholder="搜索检测项目（类别、参数、方法）"
              prefix-icon="Search"
              clearable
              size="small"
              style="width: 300px;"
            />
            <span v-if="detectionSearchKeyword" class="search-result-count">
              找到 {{ filteredCycleItems.length }} / {{ cycleItems.length }} 个项目
            </span>
          </div>
          
          <div class="detection-items-container">
            <div v-for="item in filteredCycleItems" :key="item.itemId" class="detection-item-card">
              <div class="item-header">
                <strong>{{ item.category }} - {{ item.parameter }}</strong>
                <span class="item-info">检测方法：{{ item.method }} | 周期类型：{{ item.cycleType }} | 总周期数：{{ item.cycleCount }}</span>
              </div>
              
              <div class="cycle-selection">
                <span class="cycle-label">周期：</span>
                <el-checkbox-group v-model="selectedCycleItemIds">
                  <el-checkbox
                    v-for="cycle in item.cycleItems"
                    :key="cycle.id"
                    :value="cycle.id"
                    :disabled="!isCycleSelectable(item, cycle)"
                    @change="handleCycleChange(item, cycle, $event)"
                    class="cycle-checkbox"
                  >
                    <div class="cycle-content" :class="{
                      'cycle-assigned': cycle.status === 1,
                      'cycle-completed': cycle.status === 2
                    }">
                      <div class="cycle-number">{{ cycle.cycleNumber }}</div>
                    </div>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignmentOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitAssignment" :loading="assignmentLoading">生成采样任务</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 点位确认弹窗 -->
    <PointItemConfirmation
      v-model:visible="pointConfirmationVisible"
      :project-quotation-id="selectedQuotationForConfirmation?.id"
      @confirmed="handlePointConfirmed"
    />
  </div>
</template>

<script>
import { getApprovedQuotationsPage, getQuotationCycleItems, createSamplingTask } from "@/api/sampling/assignment";
import { listUser } from "@/api/system/user";
import PointItemConfirmation from "@/components/PointItemConfirmation/index.vue";
import SamplingAssignmentWizard from "@/components/SamplingAssignmentWizard/index.vue";


export default {
  name: "SamplingAssignment",
  components: {
    PointItemConfirmation,
    SamplingAssignmentWizard
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目报价列表
      quotationList: [],
      // 用户列表
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        projectCode: null,
        customerName: null
      },
      // 分配弹窗
      assignmentOpen: false,
      legacyAssignmentOpen: false,
      assignmentLoading: false,
      selectedQuotation: null,
      cycleItems: [],
      selectedCycleItemIds: [],
      // 一键选择历史记录，用于回退操作
      batchSelectHistory: [],
      // 检测项目搜索关键词
      detectionSearchKeyword: '',
      // 点位确认弹窗
      pointConfirmationVisible: false,
      selectedQuotationForConfirmation: null,
      // 分配表单
      assignmentForm: {
        taskName: '',
        taskDescription: '',
        responsibleUserId: null,
        plannedStartTime: null,
        plannedEndTime: null
      },
      // 表单校验
      assignmentRules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    // 过滤后的检测项目列表
    filteredCycleItems() {
      if (!this.detectionSearchKeyword) {
        return this.cycleItems;
      }
      
      const keyword = this.detectionSearchKeyword.toLowerCase();
      return this.cycleItems.filter(item => {
        return (
          item.category.toLowerCase().includes(keyword) ||
          item.parameter.toLowerCase().includes(keyword) ||
          item.method.toLowerCase().includes(keyword)
        );
      });
    }
  },
  created() {
    this.getList();
    this.getUserList();
  },
  methods: {
    /** 查询项目报价列表 */
    getList(pagination) {
      // 如果有分页参数，更新查询参数
      if (pagination) {
        this.queryParams.pageNum = pagination.page;
        this.queryParams.pageSize = pagination.limit;
      }
      
      this.loading = true;
      this.quotationList = []; // Clear the list before fetching new data
      getApprovedQuotationsPage(this.queryParams).then(response => {
        let rows = [];
        let total = 0;

        if (response && response.data) {
          rows = response.data.rows || [];
          total = response.data.total || 0;
        }

        this.quotationList = rows.filter(item => item != null && typeof item === 'object');
        this.total = total;
        this.loading = false;
      }).catch(error => {
        console.error('获取项目报价列表失败:', error);
        this.quotationList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 获取用户列表 */
    getUserList() {
      listUser({ pageSize: 100000 }).then(response => {
        this.userList = response.rows || [];
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.userList = [];
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryRef");
      this.handleQuery();
    },
    /** 采样分配按钮操作 */
    handleAssignment(row) {
      if (!row) {
        console.error("handleAssignment received an undefined row.");
        return;
      }
      this.selectedQuotation = row;
      this.assignmentOpen = true;
    },

    /** 采样任务分配成功回调 */
    handleAssignmentSuccess(result) {
      this.$message.success('采样任务创建成功');
      this.getList(); // 刷新列表
    },

    /** 动态判断周期是否可选 */
    isCycleSelectable(item, cycle) {
      // 如果周期已经分配或完成，不可选
      if (cycle.status === 1 || cycle.status === 2) {
        return false;
      }
      
      // 获取同一检测项目的所有周期，按周期号排序
      const itemCycles = item.cycleItems.sort((a, b) => a.cycleNumber - b.cycleNumber);
      const currentIndex = itemCycles.findIndex(c => c.id === cycle.id);
      
      // 第一个周期总是可选的（如果状态允许）
      if (currentIndex === 0) {
        return cycle.status === 0;
      }
      
      // 检查前面的周期是否都已选择或已完成
      for (let i = 0; i < currentIndex; i++) {
        const prevCycle = itemCycles[i];
        // 前面的周期必须是已选择、已分配或已完成状态
        if (prevCycle.status === 0 && !this.selectedCycleItemIds.includes(prevCycle.id)) {
          return false;
        }
      }
      
      return cycle.status === 0;
    },

    /** 周期选择变更 */
    handleCycleChange(item, cycle, checked) {
      if (!checked) {
        // 取消选择时，同时取消后面所有已选择的周期
        const itemCycles = item.cycleItems.sort((a, b) => a.cycleNumber - b.cycleNumber);
        const currentIndex = itemCycles.findIndex(c => c.id === cycle.id);
        
        // 取消当前周期之后所有已选择的周期
        for (let i = currentIndex + 1; i < itemCycles.length; i++) {
          const nextCycle = itemCycles[i];
          const index = this.selectedCycleItemIds.indexOf(nextCycle.id);
          if (index > -1) {
            this.selectedCycleItemIds.splice(index, 1);
          }
        }
      }
      // 选择周期时不需要额外逻辑，因为isCycleSelectable已经控制了可选状态
    },
    /** 一键选择下一个可选周期 */
    handleBatchSelectNext() {
      // 记录当前选择状态，用于回退
      this.batchSelectHistory.push([...this.selectedCycleItemIds]);
      
      const newSelections = [];
      
      // 遍历过滤后的检测项目
      this.filteredCycleItems.forEach(item => {
        // 获取该项目的所有周期，按周期号排序
        const itemCycles = item.cycleItems.sort((a, b) => a.cycleNumber - b.cycleNumber);
        
        // 找到下一个可选的周期
        for (const cycle of itemCycles) {
          // 如果这个周期可选且未被选择
          if (this.isCycleSelectable(item, cycle) && !this.selectedCycleItemIds.includes(cycle.id)) {
            newSelections.push(cycle.id);
            break; // 只选择第一个可选的周期
          }
        }
      });
      
      // 添加新选择的周期
      this.selectedCycleItemIds.push(...newSelections);
      
      if (newSelections.length > 0) {
        this.$message.success(`已选择 ${newSelections.length} 个检测项目的下一周期`);
      } else {
        this.$message.info('没有可选择的下一周期');
        // 如果没有新选择，移除刚添加的历史记录
        this.batchSelectHistory.pop();
      }
    },
    /** 一键取消（回退上一次一键选择） */
    handleBatchCancel() {
      if (this.batchSelectHistory.length > 0) {
        // 恢复到上一次一键选择前的状态
        const previousState = this.batchSelectHistory.pop();
        this.selectedCycleItemIds = [...previousState];
        this.$message.success('已回退上一次一键选择');
      } else {
        // 如果没有历史记录，清空所有选择
        this.selectedCycleItemIds = [];
        this.$message.success('已清空所有选择');
      }
    },
    /** 提交分配 */
    submitAssignment() {
      this.$refs["assignmentForm"].validate(valid => {
        if (valid) {
          if (this.selectedCycleItemIds.length === 0) {
            this.$modal.msgWarning("请至少选择一个检测周期");
            return;
          }
          
          this.assignmentLoading = true;
          const assignmentData = {
            projectQuotationId: this.selectedQuotation.id,
            taskName: this.assignmentForm.taskName,
            taskDescription: this.assignmentForm.taskDescription,
            selectedCycleItemIds: this.selectedCycleItemIds,
            plannedStartTime: this.assignmentForm.plannedStartTime,
            plannedEndTime: this.assignmentForm.plannedEndTime,
            responsibleUserId: this.assignmentForm.responsibleUserId
          };
          
          createSamplingTask(assignmentData).then(response => {
            this.$modal.msgSuccess(response.msg);
            this.assignmentOpen = false;
            this.getList(); // 刷新列表
          }).finally(() => {
            this.assignmentLoading = false;
          });
        }
      });
    },
    /** 安全获取格式化委托日期 */
    getFormattedCommissionDate(row) {
      return (row && row.commissionDate) ? this.parseTime(row.commissionDate, '{y}-{m}-{d}') : '-';
    },
    /** 安全获取格式化创建时间 */
    getFormattedCreateTime(row) {
      return (row && row.createTime) ? this.parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-';
    },

    /** 点位确认按钮操作 */
    handlePointConfirmation(row) {
      if (!row) {
        console.error("handlePointConfirmation received an undefined row.");
        return;
      }
      this.selectedQuotationForConfirmation = row;
      this.pointConfirmationVisible = true;
    },

    /** 点位确认状态变更回调 */
    handlePointConfirmed(data) {
      console.log('点位确认状态变更:', data);
      // 可以在这里添加额外的处理逻辑，比如刷新列表等
    },

    /** 表格选择变更 */
    handleSelectionChange(selection) {
      // 处理表格行选择变更
      console.log('选择变更:', selection);
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.assignment-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.assignment-header h3 {
  margin: 0 0 5px 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.text-muted {
  color: var(--el-text-color-placeholder);
  font-size: 14px;
}

.text-warning {
  color: var(--el-color-warning);
  font-size: 14px;
  margin-top: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-subtitle {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-left: 15px;
}

.box-card {
  margin-top: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-regular);
  font-weight: 600;
}

.el-button--link {
  color: var(--el-color-primary);
  font-weight: 500;
}

.el-button--link:hover {
  color: var(--el-color-primary-light-3);
  background-color: var(--el-color-primary-light-9);
}

.detection-items-section {
  margin-top: 20px;
}

.detection-items-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 8px;
  background-color: var(--el-fill-color-light);
}

/* 滚动条样式美化 */
.detection-items-container::-webkit-scrollbar {
  width: 8px;
}

.detection-items-container::-webkit-scrollbar-track {
  background: var(--el-fill-color);
  border-radius: 4px;
}

.detection-items-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.detection-items-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}

/* 检测项目搜索框样式 */
.detection-search-box {
  margin-bottom: 12px;
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detection-search-box .el-input {
  border-radius: 6px;
}

.detection-search-box .el-input__wrapper {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.detection-search-box .el-input__wrapper:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.detection-search-box .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.search-result-count {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-left: 8px;
  white-space: nowrap;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detection-items-section h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.batch-operation-buttons {
  display: flex;
  gap: 8px;
}

.batch-operation-buttons .el-button {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.batch-operation-buttons .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.detection-item-card {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 6px;
  margin-bottom: 4px;
  background-color: var(--el-bg-color);
  transition: all 0.3s ease;
}

.detection-item-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-header {
  margin-bottom: 1px;
}

.item-info {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-left: 10px;
}

.cycle-selection {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 0px;
  margin-top: -1px;
}

.cycle-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-right: 10px;
  min-width: 60px;
  font-weight: 500;
}

.el-checkbox {
  margin-right: 8px;
  margin-bottom: 1px;
}

/* 周期选择框样式 */
.cycle-checkbox {
  display: inline-flex;
  align-items: center;
  margin-right: 3px;
  margin-bottom: 1px;
}

.cycle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-left: -6px;
  position: relative;
  min-width: 28px;
  min-height: 28px;
  border-radius: 3px;
  transition: all 0.3s ease;
  justify-content: center;
}

.cycle-number {
  font-size: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
  z-index: 2;
  position: relative;
  padding: 4px;
  text-align: center;
}

/* 已分配状态的背景水印 */
.cycle-content.cycle-assigned::before {
  content: '已分配';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  font-size: 8px;
  color: rgba(230, 162, 60, 0.3);
  font-weight: bold;
  z-index: 1;
  white-space: nowrap;
}

.cycle-content.cycle-assigned .cycle-number {
  color: var(--el-color-warning);
  font-weight: 700;
}

/* 已完成状态的背景水印 */
.cycle-content.cycle-completed::before {
  content: '已完成';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  font-size: 8px;
  color: rgba(103, 194, 58, 0.3);
  font-weight: bold;
  z-index: 1;
  white-space: nowrap;
}

.cycle-content.cycle-completed .cycle-number {
  color: var(--el-color-success);
  font-weight: 700;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.mb8 {
  margin-bottom: 8px;
}

/* 搜索表单样式 */
.search-form {
  background-color: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

.el-form-item {
  margin-bottom: 18px;
}

.el-input, .el-select {
  width: 100%;
}

/* 搜索表单样式 */
.el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

/* 操作按钮区域 */
.el-row.mb8 {
  background-color: var(--el-bg-color-page);
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

/* 按钮组样式优化 */
.el-row.mb8 .el-button {
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-row.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片头部样式增强 */
.box-card .el-card__header {
  background: linear-gradient(135deg, var(--el-bg-color-page) 0%, var(--el-fill-color-light) 100%);
  border-bottom: 2px solid var(--el-border-color);
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: var(--el-fill-color-light) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 12px;
  }
  
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .el-form-item .el-input,
  .el-form-item .el-select {
    width: 100% !important;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .card-subtitle {
    display: none;
  }
  
  .el-table-column {
    min-width: 80px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .batch-operation-buttons {
    width: 100%;
    justify-content: flex-start;
  }
  
  .batch-operation-buttons .el-button {
    flex: 1;
    max-width: 120px;
  }
}
</style>