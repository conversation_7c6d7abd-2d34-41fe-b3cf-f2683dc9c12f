from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime, date
from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel


class DetectionCycleItemDTO(BaseModel):
    """检测周期条目DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    id: int
    project_quotation_id: int
    project_quotation_item_point_item_id: int
    cycle_number: int
    cycle_description: Optional[str] = None
    planned_detection_date: Optional[date] = None
    actual_detection_date: Optional[date] = None
    detection_location: Optional[str] = None
    detection_method: Optional[str] = None
    sample_requirement: Optional[str] = None
    status: int
    assigned_to: Optional[int] = None
    remarks: Optional[str] = None
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None
    
    # 项目报价相关字段
    project_name: Optional[str] = None
    
    # 项目报价明细相关字段
    detection_qualification: Optional[str] = None
    detection_classification: Optional[str] = None
    detection_category: Optional[str] = None
    detection_parameter: Optional[str] = None
    detection_method: Optional[str] = None
    sample_source: Optional[str] = None
    point_name: Optional[str] = None
    cycle_type: Optional[str] = None
    status_label: Optional[str] = None
    



class DetectionCycleItemCreateDTO(BaseModel):
    """检测周期条目创建DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    project_quotation_id: int
    project_quotation_item_point_item_id: int
    cycle_number: int
    cycle_description: Optional[str] = None
    planned_detection_date: Optional[date] = None
    detection_location: Optional[str] = None
    detection_method: Optional[str] = None
    sample_requirement: Optional[str] = None
    remarks: Optional[str] = None


class DetectionCycleItemUpdateDTO(BaseModel):
    """检测周期条目更新DTO"""
    cycle_description: Optional[str] = None
    planned_detection_date: Optional[date] = None
    actual_detection_date: Optional[date] = None
    detection_location: Optional[str] = None
    detection_method: Optional[str] = None
    sample_requirement: Optional[str] = None
    status: Optional[int] = None
    assigned_to: Optional[int] = None
    remarks: Optional[str] = None


class DetectionCycleItemQueryDTO(BaseModel):
    """检测周期条目查询DTO"""
    project_quotation_id: Optional[int] = None
    project_quotation_item_point_item_id: Optional[int] = None
    cycle_number: Optional[int] = None
    status: Optional[int] = None
    planned_detection_date_from: Optional[date] = None
    planned_detection_date_to: Optional[date] = None
    assigned_to: Optional[int] = None
    page_num: int = 1
    page_size: int = 10