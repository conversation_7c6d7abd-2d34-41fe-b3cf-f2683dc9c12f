#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采样任务创建服务
负责从检测周期条目创建采样任务的业务逻辑
"""

import time
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from module_quotation.entity.do.project_quotation_item_point_item_do import ProjectQuotationItemPointItem
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember
from module_sampling.dao.sampling_task_dao import SamplingTaskDao
from module_sampling.dao.sampling_task_member_dao import SamplingTaskMemberDAO
from module_sampling.entity.vo.sampling_task_creation_vo import (
    SamplingTaskCreationModel,
    ProjectQuotationItemCycleModel,
    DetectionItemModel,
    CycleItemModel,
    SamplingTaskCreationResponseModel
)
from utils.common_util import CamelCaseUtil
from exceptions.exception import ServiceException


class SamplingTaskCreationService:
    """
    采样任务创建服务
    负责从检测周期条目创建采样任务，包括任务信息设置和周期条目关联
    """

    def __init__(self, db: AsyncSession):
        """
        初始化

        :param db: 数据库会话
        """
        self.db = db
        self.sampling_task_dao = SamplingTaskDao(db)
        self.task_member_dao = SamplingTaskMemberDAO(db)

    async def get_confirmed_point_items(self, quotation_id: int) -> List[Dict[str, Any]]:
        """
        获取项目报价的已确认点位列表

        :param quotation_id: 项目报价ID
        :return: 已确认点位列表
        """
        # 查询已确认的项目报价点位明细
        point_items_stmt = (
            select(ProjectQuotationItemPointItem)
            .where(
                ProjectQuotationItemPointItem.project_quotation_id == quotation_id,
                ProjectQuotationItemPointItem.point_confirmed == True
            )
            .order_by(ProjectQuotationItemPointItem.id)
        )
        point_items_result = await self.db.execute(point_items_stmt)
        point_items = point_items_result.scalars().all()

        result = []
        for point_item in point_items:
            result.append({
                "id": point_item.id,
                "pointName": point_item.point_name,
                "sampleSource": point_item.sample_source,
                "pointConfirmed": point_item.point_confirmed,
                "confirmedBy": point_item.confirmed_by,
                "confirmedTime": point_item.confirmed_time.isoformat() if point_item.confirmed_time else None
            })

        return result

    async def get_cycle_items_by_point_items(self, point_item_ids: List[int]) -> List[Dict[str, Any]]:
        """
        根据选定的点位明细ID获取检测周期条目

        :param point_item_ids: 点位明细ID列表
        :return: 检测周期条目列表
        """
        if not point_item_ids:
            return []

        result = []
        for point_item_id in point_item_ids:
            # 获取点位明细信息
            point_item_stmt = (
                select(ProjectQuotationItemPointItem)
                .where(ProjectQuotationItemPointItem.id == point_item_id)
            )
            point_item_result = await self.db.execute(point_item_stmt)
            point_item = point_item_result.scalar_one_or_none()

            if not point_item:
                continue

            # 查询该点位明细的检测周期条目
            cycle_items_stmt = (
                select(DetectionCycleItem)
                .where(DetectionCycleItem.project_quotation_item_point_item_id == point_item_id)
                .order_by(DetectionCycleItem.cycle_number)
            )
            cycle_items_result = await self.db.execute(cycle_items_stmt)
            cycle_items = cycle_items_result.scalars().all()

            if not cycle_items:
                continue

            # 构建周期条目列表
            cycle_item_models = []
            for i, cycle_item in enumerate(cycle_items):
                # 判断是否可选择：未分配状态且前面的周期都已分配
                is_selectable = False
                if cycle_item.status == 0:  # 未分配
                    # 检查前面的周期是否都已分配
                    if i == 0:  # 第一个周期总是可选
                        is_selectable = True
                    else:
                        # 检查前面的周期是否都已分配
                        prev_cycles_assigned = all(
                            prev_cycle.status >= 1 for prev_cycle in cycle_items[:i]
                        )
                        is_selectable = prev_cycles_assigned

                cycle_item_models.append(CycleItemModel(
                    id=cycle_item.id,
                    cycle_number=cycle_item.cycle_number,
                    status=cycle_item.status,
                    status_label=cycle_item.status_label,
                    is_selectable=is_selectable
                ))

            # 直接从点位明细获取检测项目信息（因为它包含了镜像的检测信息）
            detection_item = DetectionItemModel(
                item_id=f"{point_item.id}_{point_item.category}_{point_item.parameter}",
                point_item_id=point_item.id,
                point_name=point_item.point_name,
                category=point_item.category,
                parameter=point_item.parameter,
                method=point_item.method,
                cycle_type=point_item.cycle_type,
                cycle_count=len(cycle_items),
                cycle_items=cycle_item_models
            )

            result.append(detection_item.to_dict())

        return result

    async def get_quotation_cycle_items(self, quotation_id: int) -> List[Dict[str, Any]]:
        """
        获取项目报价的检测周期条目，按检测项目分组
        只返回已确认点位的周期条目

        :param quotation_id: 项目报价ID
        :return: 检测周期条目列表
        """
        # 查询已确认的项目报价点位明细
        point_items_stmt = (
            select(ProjectQuotationItemPointItem)
            .where(
                ProjectQuotationItemPointItem.project_quotation_id == quotation_id,
                ProjectQuotationItemPointItem.point_confirmed == True
            )
            .order_by(ProjectQuotationItemPointItem.id)
        )
        point_items_result = await self.db.execute(point_items_stmt)
        point_items = point_items_result.scalars().all()

        result = []
        for point_item in point_items:
            # 查询该点位明细的检测周期条目
            cycle_items_stmt = (
                select(DetectionCycleItem)
                .where(DetectionCycleItem.project_quotation_item_point_item_id == point_item.id)
                .order_by(DetectionCycleItem.cycle_number)
            )
            cycle_items_result = await self.db.execute(cycle_items_stmt)
            cycle_items = cycle_items_result.scalars().all()

            # 构建周期条目列表
            cycle_item_models = []
            for i, cycle_item in enumerate(cycle_items):
                # 判断是否可选择：未分配状态且前面的周期都已分配
                is_selectable = False
                if cycle_item.status == 0:  # 未分配
                    # 检查前面的周期是否都已分配
                    if i == 0:  # 第一个周期总是可选
                        is_selectable = True
                    else:
                        # 检查前面的周期是否都已分配
                        prev_cycles_assigned = all(
                            prev_cycle.status >= 1 for prev_cycle in cycle_items[:i]
                        )
                        is_selectable = prev_cycles_assigned

                cycle_item_models.append(CycleItemModel(
                    id=cycle_item.id,
                    cycle_number=cycle_item.cycle_number,
                    status=cycle_item.status,
                    status_label=cycle_item.status_label,
                    is_selectable=is_selectable
                ))

            # 构建点位明细模型
            # 处理空的cycle_type，提供默认值
            cycle_type = point_item.cycle_type if point_item.cycle_type and point_item.cycle_type.strip() else "常规"

            item_model = ProjectQuotationItemCycleModel(
                item_id=point_item.id,
                item_code=point_item.item_code,
                category=point_item.category,
                parameter=point_item.parameter,
                method=point_item.method,
                cycle_type=cycle_type,
                cycle_count=point_item.cycle_count,
                cycle_items=cycle_item_models
            )
            # 添加点位确认状态到返回数据中
            item_dict = item_model.dict()
            item_dict['point_confirmed'] = point_item.point_confirmed
            item_dict['point_name'] = point_item.point_name
            result.append(item_dict)

        return CamelCaseUtil.transform_result(result)

    async def create_sampling_task(
        self, 
        creation_data: SamplingTaskCreationModel, 
        user_id: int
    ) -> SamplingTaskCreationResponseModel:
        """
        创建采样任务
        从选定的检测周期条目创建采样任务，并建立关联关系

        :param creation_data: 采样任务创建数据
        :param user_id: 当前用户ID
        :return: 创建结果
        """
        from utils.log_util import logger
        
        try:
            logger.info(f"开始创建采样任务服务，用户ID: {user_id}")
            logger.info(f"任务名称: {creation_data.task_name}")
            logger.info(f"项目报价ID: {creation_data.project_quotation_id}")
            logger.info(f"负责人用户ID: {creation_data.responsible_user_id}")
            logger.info(f"选中的周期条目IDs: {creation_data.selected_cycle_item_ids}")
            
            # 验证选中的检测周期条目
            logger.info("开始验证检测周期条目")
            await self._validate_cycle_items(creation_data.selected_cycle_item_ids)
            logger.info("检测周期条目验证通过")

            # 生成任务编号
            logger.info("开始生成任务编号")
            task_number = await self.sampling_task_dao.generate_task_code()
            logger.info(f"生成任务编号: {task_number}")

            # 创建采样任务
            logger.info("开始创建采样任务实体")
            sampling_task = SamplingTask(
                task_code=task_number,
                task_name=creation_data.task_name,
                project_quotation_id=creation_data.project_quotation_id,
                responsible_user_id=creation_data.responsible_user_id,
                status=0,  # 0-待执行
                planned_start_date=creation_data.planned_start_time.date() if creation_data.planned_start_time else None,
                planned_end_date=creation_data.planned_end_time.date() if creation_data.planned_end_time else None,
                description=creation_data.description,
                create_by=user_id,
                create_time=datetime.now(),
                update_by=user_id,
                update_time=datetime.now()
            )

            logger.info("添加采样任务到数据库")
            self.db.add(sampling_task)
            await self.db.flush()  # 获取生成的ID
            logger.info(f"采样任务创建成功，ID: {sampling_task.id}")

            # 创建采样任务与检测周期条目的关联
            logger.info("开始创建任务与周期条目的关联")
            for cycle_item_id in creation_data.selected_cycle_item_ids:
                task_cycle_relation = SamplingTaskCycleItem(
                    sampling_task_id=sampling_task.id,
                    detection_cycle_item_id=cycle_item_id,
                    create_by=user_id,
                    create_time=datetime.now()
                )
                self.db.add(task_cycle_relation)
                logger.info(f"创建关联: 任务ID {sampling_task.id} <-> 周期条目ID {cycle_item_id}")

            # 更新检测周期条目状态为已分配
            logger.info("开始更新检测周期条目状态")
            cycle_items_stmt = (
                select(DetectionCycleItem)
                .where(DetectionCycleItem.id.in_(creation_data.selected_cycle_item_ids))
            )
            cycle_items_result = await self.db.execute(cycle_items_stmt)
            cycle_items = cycle_items_result.scalars().all()
            logger.info(f"查询到 {len(cycle_items)} 个周期条目需要更新状态")

            for cycle_item in cycle_items:
                logger.info(f"更新周期条目 {cycle_item.id} 状态从 {cycle_item.status} 到 1")
                cycle_item.status = 1  # 已分配
                cycle_item.update_by = user_id
                cycle_item.update_time = datetime.now()

            # 创建任务成员记录
            if hasattr(creation_data, 'member_user_ids') and creation_data.member_user_ids:
                logger.info(f"开始创建任务成员记录，成员数量: {len(creation_data.member_user_ids)}")
                for member_user_id in creation_data.member_user_ids:
                    task_member = SamplingTaskMember(
                        sampling_task_id=sampling_task.id,
                        user_id=member_user_id,
                        create_by=user_id,
                        create_time=datetime.now(),
                        update_by=user_id,
                        update_time=datetime.now()
                    )
                    self.db.add(task_member)
                    logger.info(f"创建任务成员记录: 任务ID {sampling_task.id} <-> 用户ID {member_user_id}")
                logger.info("任务成员记录创建完成")

            # 创建分组记录
            logger.info("开始创建采样任务分组记录")
            from module_sampling.service.sampling_task_group_service import SamplingTaskGroupService
            group_service = SamplingTaskGroupService(self.db)
            await group_service.create_groups_for_task(sampling_task.id, creation_data.selected_cycle_item_ids, user_id)
            logger.info("采样任务分组记录创建完成")

            logger.info("开始提交事务")
            await self.db.commit()
            logger.info("事务提交成功")

            result = SamplingTaskCreationResponseModel(
                success=True,
                task_id=sampling_task.id,
                task_number=task_number,
                task_name=creation_data.task_name,
                cycle_item_count=len(creation_data.selected_cycle_item_ids),
                message=f"成功创建采样任务，关联了{len(creation_data.selected_cycle_item_ids)}个检测周期条目"
            )
            logger.info(f"采样任务创建完成，返回结果: {result.dict()}")
            return result
            
        except Exception as e:
            logger.error(f"创建采样任务过程中发生异常: {str(e)}")
            logger.exception(e)
            await self.db.rollback()
            return SamplingTaskCreationResponseModel(
                success=False,
                message=f"创建采样任务失败: {str(e)}",
                task_id=None,
                task_number=None,
                task_name=None,
                cycle_item_count=0
            )

    async def _validate_cycle_items(self, cycle_item_ids: List[int]):
        """
        验证选中的检测周期条目

        :param cycle_item_ids: 检测周期条目ID列表
        """
        from utils.log_util import logger
        
        logger.info(f"开始验证检测周期条目，条目IDs: {cycle_item_ids}")
        
        if not cycle_item_ids:
            logger.error("验证失败：未选择任何检测周期条目")
            raise ServiceException(message="请至少选择一个检测周期条目")

        logger.info(f"需要验证 {len(cycle_item_ids)} 个检测周期条目")

        # 查询检测周期条目
        cycle_items_stmt = (
            select(DetectionCycleItem)
            .where(DetectionCycleItem.id.in_(cycle_item_ids))
            .order_by(DetectionCycleItem.project_quotation_item_point_item_id, DetectionCycleItem.cycle_number)
        )
        logger.info("执行数据库查询获取检测周期条目")
        cycle_items_result = await self.db.execute(cycle_items_stmt)
        cycle_items = cycle_items_result.scalars().all()
        logger.info(f"查询到 {len(cycle_items)} 个检测周期条目")

        if len(cycle_items) != len(cycle_item_ids):
            missing_ids = set(cycle_item_ids) - set(item.id for item in cycle_items)
            logger.error(f"验证失败：部分检测周期条目不存在，缺失的IDs: {missing_ids}")
            raise ServiceException(message="部分检测周期条目不存在")

        # 记录每个条目的详细信息
        for item in cycle_items:
            logger.info(f"检测周期条目 ID: {item.id}, 周期号: {item.cycle_number}, 状态: {item.status}, 点位明细ID: {item.project_quotation_item_point_item_id}")

        # 验证状态和顺序
        item_cycles = {}
        for cycle_item in cycle_items:
            if cycle_item.status != 0:
                logger.error(f"验证失败：检测周期条目{cycle_item.id}已被分配，状态: {cycle_item.status}")
                raise ServiceException(message=f"检测周期条目{cycle_item.id}已被分配，无法重复分配")

            point_item_id = cycle_item.project_quotation_item_point_item_id
            if point_item_id not in item_cycles:
                item_cycles[point_item_id] = []
            item_cycles[point_item_id].append(cycle_item.cycle_number)

        logger.info(f"按点位明细分组的周期条目: {item_cycles}")

        # 验证每个检测项目的周期选择是否连续
        for item_id, cycles in item_cycles.items():
            cycles.sort()
            logger.info(f"项目明细 {item_id} 的周期号排序后: {cycles}")
            
            # 检查周期选择是否连续（允许从任意周期开始，但必须连续）
            for i in range(1, len(cycles)):
                if cycles[i] != cycles[i-1] + 1:
                    logger.error(f"验证失败：检测项目{item_id}的周期选择不连续，在周期 {cycles[i-1]} 和 {cycles[i]} 之间断开")
                    raise ServiceException(message=f"检测项目{item_id}的周期选择必须连续，不能跳过周期")
            
            logger.info(f"项目明细 {item_id} 的周期选择验证通过，选择了连续的周期: {cycles}")
        
        logger.info("检测周期条目验证通过")