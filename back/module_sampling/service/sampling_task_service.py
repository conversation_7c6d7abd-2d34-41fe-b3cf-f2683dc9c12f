from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, date

from module_sampling.dao.sampling_task_dao import SamplingTaskDao
from module_sampling.dao.detection_cycle_item_dao import DetectionCycleItemDao
from module_sampling.dao.sampling_task_member_dao import SamplingTaskMemberDAO

from module_sampling.service.sampling_task_group_service import SamplingTaskGroupService

from module_sampling.entity.do.sampling_task_do import SamplingTask

from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
from module_sampling.dto.sampling_task_dto import (
    SamplingTaskCreateDTO, SamplingTaskUpdateDTO, SamplingTaskDTO,
    SamplingTaskQueryDTO
)
from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO
from utils.response_util import ResponseUtil
from utils.log_util import logger
from exceptions.exception import ServiceException
# from config.enums import ResponseCode  # ResponseCode 不存在，改用 message 参数
from typing import List, Optional, Tuple


class SamplingTaskService:
    """采样任务服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.sampling_task_dao = SamplingTaskDao(db)
        self.detection_cycle_item_dao = DetectionCycleItemDao(db)
        self.task_member_dao = SamplingTaskMemberDAO(db)

        self.task_group_service = SamplingTaskGroupService(db)

    async def create_sampling_task_with_items(self, task_dto: SamplingTaskCreateDTO, create_by: int) -> SamplingTaskDTO:
        """根据选择的检测项目和周期创建采样任务"""
        try:
            # 提取任务信息和项目信息
            task_name = task_dto.taskName
            description = task_dto.description
            project_quotation_id = task_dto.projectQuotationId
            items = task_dto.items

            if not all([task_name, project_quotation_id, items]):
                raise ServiceException(message="任务名称、项目报价ID和检测项目不能为空")

            # 验证项目报价ID是否存在
            # ...

            # 生成任务编号
            task_code = await self.sampling_task_dao.generate_task_code()

            # 创建采样任务
            sampling_task = SamplingTask(
                task_name=task_name,
                task_code=task_code,
                project_quotation_id=project_quotation_id,
                description=description,
                create_by=create_by
            )
            created_task = await self.sampling_task_dao.create_sampling_task(sampling_task)

            # 处理检测项目和周期
            all_cycle_ids = []
            for item in items:
                cycle_ids = item.cycles
                all_cycle_ids.extend(cycle_ids)
                for cycle_id in cycle_ids:
                    await self.sampling_task_dao.create_task_cycle_relation(
                        created_task.id, cycle_id, create_by
                    )

            # 验证并更新周期条目状态
            if all_cycle_ids:
                cycle_items = await self.detection_cycle_item_dao.get_detection_cycle_items_by_ids(all_cycle_ids)
                if len(cycle_items) != len(all_cycle_ids):
                    raise ServiceException(message="部分检测周期条目不存在")

                assigned_items = [item for item in cycle_items if item.status != 0]
                if assigned_items:
                    raise ServiceException(message="部分检测周期条目已被分配")

                await self.detection_cycle_item_dao.batch_update_status(all_cycle_ids, 1, create_by)

            # 创建分组记录
            if all_cycle_ids:
                await self.task_group_service.create_groups_for_task(created_task.id, all_cycle_ids, create_by)

            await self.db.commit()

            return self._convert_to_dto(created_task)

        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"创建采样任务失败: {str(e)}")

    async def create_sampling_task(self, task_create: SamplingTaskCreateDTO, create_by: int) -> SamplingTaskDTO:
        """创建采样任务"""
        try:
            # 验证检测周期条目是否存在且未分配
            cycle_items = await self.detection_cycle_item_dao.get_detection_cycle_items_by_ids(task_create.cycle_item_ids)
            if len(cycle_items) != len(task_create.cycle_item_ids):
                raise ServiceException(message="部分检测周期条目不存在")

            # 检查是否有已分配的周期条目
            assigned_items = [item for item in cycle_items if item.status != 0]
            if assigned_items:
                raise ServiceException(message="部分检测周期条目已被分配")

            # 生成任务编号
            task_code = await self.sampling_task_dao.generate_task_code()

            # 创建采样任务实体
            sampling_task = SamplingTask(
                task_name=task_create.task_name,
                task_code=task_code,
                project_quotation_id=task_create.project_quotation_id,
                description=task_create.description,
                responsible_user_id=task_create.responsible_user_id,
                planned_start_date=task_create.planned_start_date,
                planned_end_date=task_create.planned_end_date,
                create_by=create_by
            )

            created_task = await self.sampling_task_dao.create_sampling_task(sampling_task)

            # 创建任务与周期条目的关联
            for cycle_item_id in task_create.cycle_item_ids:
                await self.sampling_task_dao.create_task_cycle_relation(
                    created_task.id, cycle_item_id, create_by
                )

            # 更新周期条目状态为已分配
            await self.detection_cycle_item_dao.batch_update_status(
                task_create.cycle_item_ids, 1, create_by
            )

            await self.db.commit()

            # 返回创建的任务信息
            return await self._convert_to_dto(created_task)

        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"创建采样任务失败: {str(e)}")

    async def update_sampling_task(self, task_id: int, task_update: SamplingTaskUpdateDTO, update_by: int) -> SamplingTaskDTO:
        """更新采样任务"""
        try:
            # 如果只是更新加急状态，使用简化的方法
            if (task_update.is_urgent is not None and 
                all(getattr(task_update, field, None) is None for field in 
                    ['task_name', 'description', 'responsible_user_id', 'status', 'planned_start_date', 
                     'planned_end_date', 'actual_start_date', 'actual_end_date', 
                     'sampling_location', 'sampling_method', 'sample_count', 'remarks']) and
                not hasattr(task_update, 'cycle_item_ids')):
                return await self._update_urgent_status_only(task_id, task_update.is_urgent, update_by)
            
            # 获取现有任务
            existing_task = await self.sampling_task_dao.get_sampling_task_by_id(task_id)
            if not existing_task:
                raise ServiceException(message="采样任务不存在")

            # 更新基本信息
            if task_update.task_name is not None:
                existing_task.task_name = task_update.task_name
            if task_update.description is not None:
                existing_task.description = task_update.description
            if task_update.responsible_user_id is not None:
                existing_task.responsible_user_id = task_update.responsible_user_id
            if task_update.status is not None:
                existing_task.status = task_update.status
            if task_update.planned_start_date is not None:
                existing_task.planned_start_date = task_update.planned_start_date
            if task_update.planned_end_date is not None:
                existing_task.planned_end_date = task_update.planned_end_date
            if task_update.actual_start_date is not None:
                existing_task.actual_start_date = task_update.actual_start_date
            if task_update.actual_end_date is not None:
                existing_task.actual_end_date = task_update.actual_end_date
            if task_update.sampling_location is not None:
                existing_task.sampling_location = task_update.sampling_location
            if task_update.sampling_method is not None:
                existing_task.sampling_method = task_update.sampling_method
            if task_update.sample_count is not None:
                existing_task.sample_count = task_update.sample_count
            if task_update.is_urgent is not None:
                existing_task.is_urgent = task_update.is_urgent
            if task_update.remarks is not None:
                existing_task.remarks = task_update.remarks

            existing_task.update_by = update_by

            # 如果需要更新关联的周期条目
            if hasattr(task_update, 'cycle_item_ids') and task_update.cycle_item_ids is not None:
                # 获取当前关联的周期条目
                current_relations = await self.sampling_task_dao.get_task_cycle_relations(task_id)
                current_cycle_item_ids = [rel.detection_cycle_item_id for rel in current_relations]

                # 计算需要添加和删除的周期条目
                new_cycle_item_ids = set(task_update.cycle_item_ids)
                current_cycle_item_ids_set = set(current_cycle_item_ids)

                to_add = new_cycle_item_ids - current_cycle_item_ids_set
                to_remove = current_cycle_item_ids_set - new_cycle_item_ids

                # 验证新增的周期条目是否可用
                if to_add:
                    new_cycle_items = await self.detection_cycle_item_dao.get_detection_cycle_items_by_ids(list(to_add))
                    if len(new_cycle_items) != len(to_add):
                        raise ServiceException(message="部分检测周期条目不存在")

                    assigned_items = [item for item in new_cycle_items if item.status != 0]
                    if assigned_items:
                        raise ServiceException(message="部分检测周期条目已被分配")

                # 删除不再关联的周期条目关系
                if to_remove:
                    # 将这些周期条目状态改为未分配
                    await self.detection_cycle_item_dao.batch_update_status(list(to_remove), 0, update_by)

                # 添加新的关联关系
                if to_add:
                    for cycle_item_id in to_add:
                        await self.sampling_task_dao.create_task_cycle_relation(task_id, cycle_item_id, update_by)
                    # 将新关联的周期条目状态改为已分配
                    await self.detection_cycle_item_dao.batch_update_status(list(to_add), 1, update_by)

                # 如果有删除的关联，需要删除关联记录
                if to_remove:
                    await self.sampling_task_dao.delete_task_cycle_relations(task_id)
                    # 重新创建保留的关联
                    remaining_ids = new_cycle_item_ids
                    for cycle_item_id in remaining_ids:
                        await self.sampling_task_dao.create_task_cycle_relation(task_id, cycle_item_id, update_by)

            # 更新任务组员
            if task_update.member_user_ids is not None:
                await self._update_task_members(task_id, task_update.member_user_ids, update_by)

            # 更新任务
            updated_task = await self.sampling_task_dao.update_sampling_task(existing_task)

            await self.db.commit()

            return await self._convert_to_dto(existing_task)

        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"更新采样任务失败: {str(e)}")

    async def delete_sampling_task(self, task_id: int, delete_by: int) -> bool:
        """删除采样任务"""
        try:
            # 获取任务信息
            task = await self.sampling_task_dao.get_sampling_task_by_id(task_id)
            if not task:
                raise ServiceException(message="采样任务不存在")

            # 获取关联的周期条目
            relations = await self.sampling_task_dao.get_task_cycle_relations(task_id)
            cycle_item_ids = [rel.detection_cycle_item_id for rel in relations]

            # 将关联的周期条目状态改为未分配
            if cycle_item_ids:
                await self.detection_cycle_item_dao.batch_update_status(cycle_item_ids, 0, delete_by)

            # 删除任务（级联删除关联关系）
            result = await self.sampling_task_dao.delete_sampling_task(task_id)

            await self.db.commit()

            return result

        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"删除采样任务失败: {str(e)}")

    async def get_sampling_task_by_id(self, task_id: int) -> Optional[SamplingTaskDTO]:
        """根据ID获取采样任务"""
        task = await self.sampling_task_dao.get_sampling_task_by_id(task_id)
        if task:
            return await self._convert_to_dto(task)
        return None

    async def page_sampling_tasks(self, query_dto: SamplingTaskQueryDTO, page: int = 1, size: int = 10) -> Tuple[List[SamplingTaskDTO], int]:
        """分页查询采样任务"""
        tasks, total = await self.sampling_task_dao.page_sampling_tasks(
            page=page,
            size=size,
            task_name=query_dto.task_name,
            task_code=query_dto.task_code,
            project_quotation_id=query_dto.project_quotation_id,
            responsible_user_id=query_dto.responsible_user_id,
            status=query_dto.status,
            planned_start_date_from=query_dto.planned_start_date_from,
            planned_start_date_to=query_dto.planned_start_date_to,
            planned_end_date_from=query_dto.planned_end_date_from,
            planned_end_date_to=query_dto.planned_end_date_to
        )

        task_dtos = [await self._convert_to_dto(task) for task in tasks]
        return task_dtos, total

    async def page_sampling_tasks_enhanced(self, query_params: dict) -> dict:
        """增强版分页查询采样任务，参考项目报价列表的实现"""
        from sqlalchemy import select, and_, func, or_
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_admin.entity.do.user_do import SysUser
        from utils.camel_case_util import CamelCaseUtil

        # 构建查询条件
        conditions = []

        # 基本任务条件
        if query_params.get('task_name'):
            conditions.append(SamplingTask.task_name.like(f"%{query_params['task_name']}%"))
        if query_params.get('task_code'):
            conditions.append(SamplingTask.task_code.like(f"%{query_params['task_code']}%"))
        if query_params.get('project_quotation_id'):
            conditions.append(SamplingTask.project_quotation_id == query_params['project_quotation_id'])
        if query_params.get('responsible_user_id'):
            conditions.append(SamplingTask.responsible_user_id == query_params['responsible_user_id'])
        if query_params.get('status') is not None:
            conditions.append(SamplingTask.status == query_params['status'])

        # 项目相关条件
        if query_params.get('project_name'):
            conditions.append(ProjectQuotation.project_name.like(f"%{query_params['project_name']}%"))
        if query_params.get('project_code'):
            conditions.append(ProjectQuotation.project_code.like(f"%{query_params['project_code']}%"))
        if query_params.get('customer_name'):
            conditions.append(ProjectQuotation.customer_name.like(f"%{query_params['customer_name']}%"))

        # 用户相关条件
        if query_params.get('assigned_user_name'):
            conditions.append(SysUser.nick_name.like(f"%{query_params['assigned_user_name']}%"))

        # 日期条件
        if query_params.get('planned_start_date_from'):
            conditions.append(SamplingTask.planned_start_date >= query_params['planned_start_date_from'])
        if query_params.get('planned_start_date_to'):
            conditions.append(SamplingTask.planned_start_date <= query_params['planned_start_date_to'])
        if query_params.get('planned_end_date_from'):
            conditions.append(SamplingTask.planned_end_date >= query_params['planned_end_date_from'])
        if query_params.get('planned_end_date_to'):
            conditions.append(SamplingTask.planned_end_date <= query_params['planned_end_date_to'])
        if query_params.get('create_time_from'):
            conditions.append(SamplingTask.create_time >= query_params['create_time_from'])
        if query_params.get('create_time_to'):
            conditions.append(SamplingTask.create_time <= query_params['create_time_to'])

        # 构建查询语句，关联项目报价和用户表
        stmt = select(SamplingTask, ProjectQuotation, SysUser).join(
            ProjectQuotation, SamplingTask.project_quotation_id == ProjectQuotation.id
        ).outerjoin(
            SysUser, SamplingTask.responsible_user_id == SysUser.user_id
        ).where(and_(*conditions)).order_by(SamplingTask.create_time.desc())

        # 计算总数
        count_stmt = select(func.count()).select_from(
            SamplingTask.join(ProjectQuotation, SamplingTask.project_quotation_id == ProjectQuotation.id)
            .outerjoin(SysUser, SamplingTask.responsible_user_id == SysUser.user_id)
        ).where(and_(*conditions))

        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar() or 0

        # 分页查询
        page = query_params.get('page', 1)
        size = query_params.get('size', 10)
        stmt = stmt.offset((page - 1) * size).limit(size)

        result = await self.db.execute(stmt)
        rows = result.all()

        # 构建返回数据
        task_list = []
        for row in rows:
            task, quotation, user = row

            # 获取任务关联的检测周期条目
            cycle_items_stmt = select(SamplingTaskCycleItem, DetectionCycleItem).join(
                DetectionCycleItem, SamplingTaskCycleItem.detection_cycle_item_id == DetectionCycleItem.id
            ).where(SamplingTaskCycleItem.sampling_task_id == task.id)

            cycle_items_result = await self.db.execute(cycle_items_stmt)
            cycle_items = cycle_items_result.all()

            # 构建任务数据
            task_dict = {
                "id": task.id,
                "task_name": task.task_name,
                "task_code": task.task_code,
                "project_quotation_id": task.project_quotation_id,
                "description": task.description,
                "responsible_user_id": task.responsible_user_id,
                "assigned_user_name": user.nick_name if user else None,
                "status": self._get_status_value(task.status),
                "status_label": self._get_status_label(task.status),
                "planned_start_date": task.planned_start_date,
                "planned_end_date": task.planned_end_date,
                "actual_start_date": task.actual_start_date,
                "actual_end_date": task.actual_end_date,
                "create_by": task.create_by,
                "create_time": task.create_time,
                "update_by": task.update_by,
                "update_time": task.update_time,
                # 项目报价信息
                "project_quotation": {
                    "id": quotation.id,
                    "project_name": quotation.project_name,
                    "project_code": quotation.project_code,
                    "customer_name": quotation.customer_name,
                    "customer_contact": quotation.customer_contact,
                    "customer_phone": quotation.customer_phone,
                    "status": quotation.status
                } if quotation else None,
                # 检测周期条目
                "cycle_items": [
                    CamelCaseUtil.transform_result({
                        "id": cycle_item.id,
                        "item_code": cycle_item.item_code,
                        "category": cycle_item.category,
                        "parameter": cycle_item.parameter,
                        "method": cycle_item.method,
                        "point_name": cycle_item.point_name,
                        "cycle_type": cycle_item.cycle_type,
                        "cycle_count": cycle_item.cycle_count,
                        "status": self._get_cycle_status_value(cycle_item.status),
                        "status_label": self._get_cycle_status_label(cycle_item.status)
                    }) for _, cycle_item in cycle_items
                ]
            }

            # 使用驼峰命名转换
            task_list.append(CamelCaseUtil.transform_result(task_dict))

        return {
            "rows": task_list,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }
        return task_dto

    async def _update_urgent_status_only(self, task_id: int, is_urgent: bool, update_by: int) -> SamplingTaskDTO:
        """仅更新加急状态的简化方法，避免复杂的DTO转换"""
        try:
            from sqlalchemy import text
            
            # 使用原生SQL更新加急状态，避免ORM的复杂性
            update_sql = """
                UPDATE sampling_task 
                SET is_urgent = :is_urgent, update_by = :update_by, update_time = NOW() 
                WHERE id = :task_id
            """
            
            result = await self.db.execute(text(update_sql), {
                "is_urgent": is_urgent,
                "update_by": update_by,
                "task_id": task_id
            })
            
            if result.rowcount == 0:
                raise ServiceException(message="采样任务不存在")
            
            await self.db.commit()
            
            # 返回简化的DTO，只包含基本信息
            query_sql = "SELECT id, task_name, task_code, is_urgent, update_time, project_quotation_id, status FROM sampling_task WHERE id = :task_id"
            task_result = await self.db.execute(text(query_sql), {"task_id": task_id})
            task_row = task_result.fetchone()
            
            if not task_row:
                raise ServiceException(message="采样任务不存在")
            
            # 创建简化的DTO
            from module_sampling.dto.sampling_task_dto import SamplingTaskDTO
            task_dto = SamplingTaskDTO(
                id=task_row[0],
                task_name=task_row[1],
                task_code=task_row[2],
                is_urgent=bool(task_row[3]),
                update_time=task_row[4],
                project_quotation_id=task_row[5],
                status=task_row[6]
            )
            
            return task_dto
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"更新加急状态失败: {str(e)}")




    async def get_tasks_by_project_quotation_id(self, project_quotation_id: int) -> List[SamplingTaskDTO]:
        """根据项目报价ID获取采样任务列表"""
        tasks = await self.sampling_task_dao.get_tasks_by_project_quotation_id(project_quotation_id)
        return [await self._convert_to_dto(task) for task in tasks]

    async def get_tasks_by_responsible_user_id(self, responsible_user_id: int) -> List[SamplingTaskDTO]:
        """根据负责人用户ID获取采样任务列表"""
        tasks = await self.sampling_task_dao.get_tasks_by_responsible_user_id(responsible_user_id)
        return [await self._convert_to_dto(task) for task in tasks]

    async def get_tasks_by_executor_user_id(self, executor_user_id: int) -> List[SamplingTaskDTO]:
        """根据执行人用户ID获取采样任务列表（通过任务成员表关联）"""
        tasks = await self.sampling_task_dao.get_tasks_by_executor_user_id(executor_user_id)
        return [await self._convert_to_dto(task) for task in tasks]

    async def get_task_status_stats_by_user(self, user_id: int) -> dict:
        """获取用户的任务状态统计（基于执行人角色）"""
        tasks = await self.sampling_task_dao.get_tasks_by_executor_user_id(user_id)

        # 初始化统计数据
        stats = {
            "total": 0,
            "pending": 0,      # 待执行 (status = 0)
            "inProgress": 0,   # 执行中 (status = 1)
            "completed": 0     # 已完成 (status = 2)
        }

        # 统计各状态的任务数量
        for task in tasks:
            stats["total"] += 1

            if task.status == 0:
                stats["pending"] += 1
            elif task.status == 1:
                stats["inProgress"] += 1
            elif task.status == 2:
                stats["completed"] += 1

        return stats

    def _convert_status_to_string(self, status: int) -> str:
        """将整数状态转换为字符串状态"""
        status_mapping = {
            0: "pending",      # 待执行
            1: "in_progress",  # 执行中
            2: "completed"     # 已完成
        }
        return status_mapping.get(status, "pending")

    def _get_status_value(self, status: int) -> int:
        """获取状态值"""
        return status

    def _get_status_label(self, status: int) -> str:
        """获取状态标签"""
        status_mapping = {
            0: "待执行",
            1: "执行中", 
            2: "已完成"
        }
        return status_mapping.get(status, "待执行")

    def _get_cycle_status_value(self, status: int) -> int:
        """获取检测周期条目状态值"""
        return status

    def _get_cycle_status_label(self, status: int) -> str:
        """获取检测周期条目状态标签"""
        status_mapping = {
            0: "未分配",
            1: "已分配",
            2: "执行中",
            3: "已完成"
        }
        return status_mapping.get(status, "未分配")

    async def _convert_to_dto(self, task: SamplingTask) -> SamplingTaskDTO:
        """将实体转换为DTO"""
        task_dto = SamplingTaskDTO(
            id=task.id,
            project_quotation_id=task.project_quotation_id,
            task_name=task.task_name,
            task_code=task.task_code,
            description=task.description,
            planned_start_date=task.planned_start_date,
            planned_end_date=task.planned_end_date,
            actual_start_date=task.actual_start_date,
            actual_end_date=task.actual_end_date,
            sampling_location=getattr(task, 'sampling_location', None),
            sampling_method=getattr(task, 'sampling_method', None),
            sample_count=getattr(task, 'sample_count', None),
            status=task.status,
            status_label=self._get_status_label(task.status),
            remarks=getattr(task, 'remarks', None),
            is_urgent=task.is_urgent or False,
            create_by=task.create_by,
            create_time=task.create_time,
            update_by=task.update_by,
            update_time=datetime.now()  # 使用当前时间，避免延迟加载问题
        )

        # 设置关联信息
        if hasattr(task, 'project_quotation') and task.project_quotation:
            task_dto.project_name = task.project_quotation.project_name
            task_dto.project_code = task.project_quotation.project_code
        else:
            task_dto.project_name = None
            task_dto.project_code = None

        task_dto.responsible_user_id = task.responsible_user_id

        # 设置负责人信息
        if hasattr(task, 'responsible_user') and task.responsible_user:
            task_dto.responsible_user_name = task.responsible_user.nick_name
        else:
            task_dto.responsible_user_name = None

        # 查询关联的检测周期条目
        task_cycle_relations = await self.sampling_task_dao.get_task_cycle_relations(task.id)
        cycle_items = []

        if task_cycle_relations:
            # 导入DetectionCycleItemService来转换DTO
            from module_sampling.service.detection_cycle_item_service import DetectionCycleItemService
            detection_service = DetectionCycleItemService(self.db)

            for relation in task_cycle_relations:
                if relation.detection_cycle_item:
                    cycle_item_dto = detection_service._convert_to_dto(relation.detection_cycle_item)
                    cycle_items.append(cycle_item_dto)

        task_dto.cycle_items = cycle_items

        # 查询任务组员
        from utils.common_util import CamelCaseUtil
        member_users = await self.get_task_members(task.id)
        # 转换为驼峰形式
        task_dto.member_users = [CamelCaseUtil.transform_result(member) for member in member_users]

        # 查询任务分组信息（替代原来的执行人指派）
        task_groups = await self.task_group_service.get_groups_by_task_id(task.id)
        group_dtos = []

        if task_groups:
            for group in task_groups:
                group_dict = {
                    'id': group.id,
                    'groupCode': group.group_code,  # 添加分组编号
                    'cycleNumber': group.cycle_number,
                    'cycleType': group.cycle_type,
                    'detectionCategory': group.detection_category,
                    'pointName': group.point_name,
                    'cycleItemIds': group.cycle_item_ids,
                    'assignedUserIds': group.assigned_user_ids,
                    'assignedUserNames': group.assigned_user_names,
                    'status': group.status  # 添加分组状态
                }
                group_dtos.append(group_dict)

        task_dto.task_groups = group_dtos

        return task_dto

    async def _update_task_members(self, task_id: int, member_user_ids: List[int], update_by: int):
        """更新任务组员"""
        from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember

        # 删除现有组员
        await self.task_member_dao.delete_members_by_task_id(task_id)

        # 添加新组员
        if member_user_ids:
            members = []
            for user_id in member_user_ids:
                member = SamplingTaskMember(
                    sampling_task_id=task_id,
                    user_id=user_id,
                    create_by=update_by,
                    update_by=update_by
                )
                members.append(member)

            await self.task_member_dao.batch_create_members(members)

    async def get_task_members(self, task_id: int) -> List[dict]:
        """获取任务组员列表"""
        from sqlalchemy import select
        from module_admin.entity.do.user_do import SysUser

        members = await self.task_member_dao.get_members_by_task_id(task_id)

        # 获取用户信息
        if not members:
            return []

        user_ids = [member.user_id for member in members]

        # 查询用户信息
        user_query = select(SysUser).where(
            SysUser.user_id.in_(user_ids),
            SysUser.del_flag == '0'
        )
        result = await self.db.execute(user_query)
        users = result.scalars().all()

        # 创建用户ID到用户信息的映射
        user_map = {user.user_id: user for user in users}

        return [
            {
                'user_id': member.user_id,
                'user_name': user_map.get(member.user_id).user_name if user_map.get(member.user_id) else f'user{member.user_id}',
                'nick_name': user_map.get(member.user_id).nick_name if user_map.get(member.user_id) else f'用户{member.user_id}',
                'email': user_map.get(member.user_id).email if user_map.get(member.user_id) else '',
                'phonenumber': user_map.get(member.user_id).phonenumber if user_map.get(member.user_id) else '',
                'dept_id': user_map.get(member.user_id).dept_id if user_map.get(member.user_id) else None
            }
            for member in members
        ]

    async def add_task_member(self, task_id: int, user_id: int, create_by: int) -> bool:
        """添加任务组员"""
        from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember

        # 检查是否已存在
        existing = await self.task_member_dao.get_member_by_task_and_user(task_id, user_id)
        if existing:
            return False

        member = SamplingTaskMember(
            sampling_task_id=task_id,
            user_id=user_id,
            create_by=create_by,
            update_by=create_by
        )

        await self.task_member_dao.create_member(member)
        return True

    async def remove_task_member(self, task_id: int, user_id: int) -> bool:
        """移除任务组员"""
        count = await self.task_member_dao.delete_member_by_task_and_user(task_id, user_id)
        return count > 0