from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup


class SamplingTaskGroupDAO:
    """采样任务分组DAO"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_group(self, group: SamplingTaskGroup) -> SamplingTaskGroup:
        """创建分组记录"""
        self.db.add(group)
        await self.db.flush()
        await self.db.refresh(group)
        return group
    
    async def batch_create_groups(self, groups: List[SamplingTaskGroup]) -> List[SamplingTaskGroup]:
        """批量创建分组记录"""
        self.db.add_all(groups)
        await self.db.flush()
        for group in groups:
            await self.db.refresh(group)
        return groups
    
    async def update_group(self, group: SamplingTaskGroup) -> SamplingTaskGroup:
        """更新分组记录"""
        await self.db.flush()
        await self.db.refresh(group)
        return group
    
    async def get_group_by_id(self, group_id: int) -> Optional[SamplingTaskGroup]:
        """根据ID获取分组记录"""
        stmt = select(SamplingTaskGroup).where(SamplingTaskGroup.id == group_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_groups_by_task_id(self, task_id: int) -> List[SamplingTaskGroup]:
        """根据任务ID获取所有分组记录"""
        stmt = select(SamplingTaskGroup).where(
            SamplingTaskGroup.sampling_task_id == task_id
        ).order_by(
            SamplingTaskGroup.cycle_number,
            SamplingTaskGroup.detection_category,
            SamplingTaskGroup.point_name
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_group_by_group_key(
        self,
        task_id: int,
        cycle_number: int,
        cycle_type: Optional[str],
        detection_category: Optional[str],
        point_name: Optional[str]
    ) -> Optional[SamplingTaskGroup]:
        """根据分组键获取分组记录"""
        stmt = select(SamplingTaskGroup).where(
            and_(
                SamplingTaskGroup.sampling_task_id == task_id,
                SamplingTaskGroup.cycle_number == cycle_number,
                SamplingTaskGroup.cycle_type == cycle_type,
                SamplingTaskGroup.detection_category == detection_category,
                SamplingTaskGroup.point_name == point_name
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_groups_by_user_id(self, user_id: int) -> List[SamplingTaskGroup]:
        """根据用户ID获取分配给该用户的分组记录"""
        # 先获取所有有assigned_user_ids的分组
        stmt = select(SamplingTaskGroup).where(
            SamplingTaskGroup.assigned_user_ids.isnot(None)
        ).order_by(SamplingTaskGroup.create_time.desc())
        result = await self.db.execute(stmt)
        all_groups = result.scalars().all()

        # 在应用层过滤，精确匹配用户ID
        import json
        filtered_groups = []
        for group in all_groups:
            if group.assigned_user_ids:
                try:
                    assigned_ids = json.loads(group.assigned_user_ids)
                    if isinstance(assigned_ids, list) and user_id in assigned_ids:
                        filtered_groups.append(group)
                except (json.JSONDecodeError, TypeError):
                    # 如果JSON解析失败，跳过该分组
                    continue

        return filtered_groups
    
    async def delete_group(self, group_id: int) -> bool:
        """删除分组记录"""
        group = await self.get_group_by_id(group_id)
        if group:
            await self.db.delete(group)
            await self.db.flush()
            return True
        return False
    
    async def delete_groups_by_task_id(self, task_id: int) -> int:
        """删除任务的所有分组记录"""
        stmt = select(SamplingTaskGroup).where(SamplingTaskGroup.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        groups = result.scalars().all()
        
        count = len(groups)
        for group in groups:
            await self.db.delete(group)
        
        await self.db.flush()
        return count

    async def get_all_groups(self, task_name: Optional[str] = None, status: Optional[str] = None,
                           page_num: int = 1, page_size: int = 10) -> List[SamplingTaskGroup]:
        """获取所有分组记录（管理员权限）"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask

        # 构建查询
        stmt = select(SamplingTaskGroup).join(
            SamplingTask, SamplingTaskGroup.sampling_task_id == SamplingTask.id
        )

        # 添加过滤条件
        if task_name:
            stmt = stmt.where(SamplingTask.task_name.like(f'%{task_name}%'))
        if status:
            stmt = stmt.where(SamplingTask.status == status)

        # 添加分页
        offset = (page_num - 1) * page_size
        stmt = stmt.offset(offset).limit(page_size)

        # 按创建时间倒序排列
        stmt = stmt.order_by(SamplingTaskGroup.create_time.desc())

        result = await self.db.execute(stmt)
        return result.scalars().all()
