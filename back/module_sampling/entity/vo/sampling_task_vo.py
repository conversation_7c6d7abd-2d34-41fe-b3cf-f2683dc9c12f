from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import date, datetime


class DetectionCycleItemVO(BaseModel):
    """检测周期条目VO"""
    id: Optional[int] = None
    project_quotation_id: int = Field(..., description="项目报价ID")
    project_quotation_item_point_item_id: int = Field(..., description="项目报价点位明细ID")
    cycle_number: int = Field(..., description="周期序号")
    status: int = Field(default=0, description="状态：0-未分配，1-已分配，2-已完成")
    status_label: Optional[str] = Field(None, description="状态标签")
    
    # 关联信息
    project_name: Optional[str] = Field(None, description="项目名称")
    detection_qualification: Optional[str] = Field(None, description="检测资质")
    detection_classification: Optional[str] = Field(None, description="检测分类")
    detection_category: Optional[str] = Field(None, description="检测类别")
    detection_parameter: Optional[str] = Field(None, description="检测参数")
    detection_method: Optional[str] = Field(None, description="检测方法")
    sample_source: Optional[str] = Field(None, description="样品来源")
    point_name: Optional[str] = Field(None, description="点位名称")
    
    class Config:
        from_attributes = True


class SamplingTaskCreateVO(BaseModel):
    """创建采样任务VO"""
    task_name: str = Field(..., description="任务名称")
    project_quotation_id: int = Field(..., description="项目报价ID")
    description: Optional[str] = Field(None, description="任务描述")
    responsible_user_id: Optional[int] = Field(None, description="负责人用户ID")
    planned_start_date: Optional[date] = Field(None, description="计划开始日期")
    planned_end_date: Optional[date] = Field(None, description="计划结束日期")
    cycle_item_ids: List[int] = Field(..., description="关联的检测周期条目ID列表")
    
    class Config:
        from_attributes = True


class SamplingTaskUpdateVO(BaseModel):
    """更新采样任务VO"""
    task_name: Optional[str] = Field(None, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    responsible_user_id: Optional[int] = Field(None, description="负责人用户ID")
    status: Optional[int] = Field(None, description="任务状态")
    planned_start_date: Optional[date] = Field(None, description="计划开始日期")
    planned_end_date: Optional[date] = Field(None, description="计划结束日期")
    actual_start_date: Optional[date] = Field(None, description="实际开始日期")
    actual_end_date: Optional[date] = Field(None, description="实际结束日期")
    cycle_item_ids: Optional[List[int]] = Field(None, description="关联的检测周期条目ID列表")
    
    class Config:
        from_attributes = True


class SamplingTaskVO(BaseModel):
    """采样任务VO"""
    id: Optional[int] = None
    task_name: str = Field(..., description="任务名称")
    task_code: str = Field(..., description="任务编号")
    project_quotation_id: int = Field(..., description="项目报价ID")
    description: Optional[str] = Field(None, description="任务描述")
    responsible_user_id: Optional[int] = Field(None, description="负责人用户ID")
    status: int = Field(default=0, description="任务状态")
    status_label: Optional[str] = Field(None, description="状态标签")
    planned_start_date: Optional[date] = Field(None, description="计划开始日期")
    planned_end_date: Optional[date] = Field(None, description="计划结束日期")
    actual_start_date: Optional[date] = Field(None, description="实际开始日期")
    actual_end_date: Optional[date] = Field(None, description="实际结束日期")
    
    # 关联信息
    project_name: Optional[str] = Field(None, description="项目名称")
    responsible_user_name: Optional[str] = Field(None, description="负责人姓名")
    cycle_items: Optional[List[DetectionCycleItemVO]] = Field(None, description="关联的检测周期条目")
    
    # 审计信息
    create_by: Optional[int] = Field(None, description="创建人")
    create_time: Optional[datetime] = Field(None, description="创建时间")
    update_by: Optional[int] = Field(None, description="更新人")
    update_time: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class SamplingTaskQueryVO(BaseModel):
    """采样任务查询VO"""
    task_name: Optional[str] = Field(None, description="任务名称")
    task_code: Optional[str] = Field(None, description="任务编号")
    project_quotation_id: Optional[int] = Field(None, description="项目报价ID")
    responsible_user_id: Optional[int] = Field(None, description="负责人用户ID")
    status: Optional[int] = Field(None, description="任务状态")
    planned_start_date_from: Optional[date] = Field(None, description="计划开始日期（起）")
    planned_start_date_to: Optional[date] = Field(None, description="计划开始日期（止）")
    planned_end_date_from: Optional[date] = Field(None, description="计划结束日期（起）")
    planned_end_date_to: Optional[date] = Field(None, description="计划结束日期（止）")
    
    class Config:
        from_attributes = True


class CycleItemAssignmentVO(BaseModel):
    """周期条目分配VO"""
    project_quotation_id: int = Field(..., description="项目报价ID")
    cycle_item_ids: List[int] = Field(..., description="要分配的检测周期条目ID列表")
    task_name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    responsible_user_id: Optional[int] = Field(None, description="负责人用户ID")
    member_user_ids: Optional[List[int]] = Field(default_factory=list, description="任务成员用户ID列表")
    planned_start_date: Optional[date] = Field(None, description="计划开始日期")
    planned_end_date: Optional[date] = Field(None, description="计划结束日期")
    
    class Config:
        from_attributes = True