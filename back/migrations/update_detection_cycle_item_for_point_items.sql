-- 更新检测周期条目表以支持基于点位明细的生成
-- 由于表中已有数据，我们需要先删除外键约束，清空表，然后修改结构

-- 1. 备份现有数据（可选）
-- CREATE TABLE detection_cycle_item_backup AS SELECT * FROM detection_cycle_item;

-- 2. 删除相关表的外键约束
ALTER TABLE sampling_task_cycle_item DROP FOREIGN KEY sampling_task_cycle_item_ibfk_2;

-- 3. 清空相关表数据
TRUNCATE TABLE sampling_task_cycle_item;

-- 4. 清空主表数据
TRUNCATE TABLE detection_cycle_item;

-- 5. 添加新字段
ALTER TABLE detection_cycle_item
ADD COLUMN project_quotation_item_point_item_id INT NOT NULL COMMENT '项目报价点位明细ID' AFTER project_quotation_id;

-- 6. 添加外键约束
ALTER TABLE detection_cycle_item
ADD CONSTRAINT fk_detection_cycle_item_point_item
FOREIGN KEY (project_quotation_item_point_item_id)
REFERENCES project_quotation_item_point_item(id)
ON DELETE CASCADE;

-- 7. 添加索引
CREATE INDEX idx_detection_cycle_item_point_item_id
ON detection_cycle_item(project_quotation_item_point_item_id);

-- 8. 删除旧字段
ALTER TABLE detection_cycle_item
DROP COLUMN project_quotation_item_id;

-- 9. 重新添加相关表的外键约束
ALTER TABLE sampling_task_cycle_item
ADD CONSTRAINT sampling_task_cycle_item_ibfk_2
FOREIGN KEY (detection_cycle_item_id)
REFERENCES detection_cycle_item(id);
