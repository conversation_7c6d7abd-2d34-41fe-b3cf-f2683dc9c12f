-- 完成检测周期条目表的更新

-- 1. 删除旧的外键约束
ALTER TABLE detection_cycle_item DROP FOREIGN KEY detection_cycle_item_ibfk_2;

-- 2. 删除旧的唯一索引
ALTER TABLE detection_cycle_item DROP INDEX uk_quotation_item_cycle;

-- 3. 删除旧的索引
ALTER TABLE detection_cycle_item DROP INDEX idx_detection_cycle_item_project_quotation_item_id;

-- 4. 删除旧字段
ALTER TABLE detection_cycle_item DROP COLUMN project_quotation_item_id;

-- 5. 添加新的唯一索引（基于点位明细ID和周期号）
ALTER TABLE detection_cycle_item
ADD UNIQUE KEY uk_point_item_cycle (project_quotation_item_point_item_id, cycle_number);

-- 6. 重新添加相关表的外键约束（如果不存在）
-- 注意：这个约束可能已经存在，如果报错可以忽略
-- ALTER TABLE sampling_task_cycle_item
-- ADD CONSTRAINT sampling_task_cycle_item_ibfk_2
-- FOREIGN KEY (detection_cycle_item_id)
-- REFERENCES detection_cycle_item(id);
