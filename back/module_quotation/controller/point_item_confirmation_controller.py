"""
点位确认控制器
"""

from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from config.get_db import get_db
from module_admin.service.login_service import LoginService, CurrentUserModel
from module_quotation.service.point_item_confirmation_service import PointItemConfirmationService
from module_quotation.dto.point_item_confirmation_dto import (
    PointItemConfirmationDTO,
    PointItemRenameDTO,
    PointItemConfirmDTO,
    BatchPointItemConfirmDTO,
    BatchPointItemConfirmByNameDTO,
    BatchPointItemRenameByNameDTO
)
from utils.response_util import ResponseUtil
from module_admin.annotation.log_annotation import Log
from config.enums import BusinessType
from pydantic_validation_decorator import ValidateFields


pointItemConfirmationController = APIRouter(prefix="/quotation/point-item-confirmation", tags=["点位确认管理"])


@pointItemConfirmationController.get("/project/{project_quotation_id}")
async def get_point_items_by_quotation_id(
    request: Request,
    project_quotation_id: int,
    point_confirmed: Optional[bool] = Query(None, description="点位确认状态筛选"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据项目报价ID获取点位明细列表"""
    service = PointItemConfirmationService(query_db)
    point_items = await service.get_point_items_by_quotation_id(project_quotation_id, point_confirmed)
    
    return ResponseUtil.success(data=point_items)


@pointItemConfirmationController.get("/{point_item_id}")
async def get_point_item_by_id(
    request: Request,
    point_item_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """根据ID获取点位明细"""
    service = PointItemConfirmationService(query_db)
    point_item = await service.get_point_item_by_id(point_item_id)
    
    if not point_item:
        return ResponseUtil.error(msg="点位明细不存在")
    
    return ResponseUtil.success(data=point_item)


@pointItemConfirmationController.put("/rename")
@Log(title='点位确认管理', business_type=BusinessType.UPDATE)
async def rename_point_item(
    request: Request,
    rename_dto: PointItemRenameDTO,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """重命名点位"""
    service = PointItemConfirmationService(query_db)
    point_item = await service.rename_point_item(
        rename_dto.point_item_id,
        rename_dto.new_point_name,
        current_user.user.user_name
    )
    
    return ResponseUtil.success(data=point_item, msg="点位重命名成功")


@pointItemConfirmationController.put("/confirm")
@Log(title='点位确认管理', business_type=BusinessType.UPDATE)
async def confirm_point_item(
    request: Request,
    confirm_dto: PointItemConfirmDTO,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """确认/取消确认点位"""
    service = PointItemConfirmationService(query_db)
    point_item = await service.confirm_point_item(
        confirm_dto.point_item_id,
        confirm_dto.confirmed,
        current_user.user.user_name
    )
    
    action = "确认" if confirm_dto.confirmed else "取消确认"
    return ResponseUtil.success(data=point_item, msg=f"点位{action}成功")


@pointItemConfirmationController.put("/batch-confirm")
@Log(title='点位确认管理', business_type=BusinessType.UPDATE)
async def batch_confirm_point_items(
    request: Request,
    batch_confirm_dto: BatchPointItemConfirmDTO,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """批量确认/取消确认点位"""
    service = PointItemConfirmationService(query_db)
    point_items = await service.batch_confirm_point_items(
        batch_confirm_dto.point_item_ids,
        batch_confirm_dto.confirmed,
        current_user.user.user_name
    )
    
    action = "确认" if batch_confirm_dto.confirmed else "取消确认"
    return ResponseUtil.success(data=point_items, msg=f"批量{action}点位成功")


@pointItemConfirmationController.get("/project/{project_quotation_id}/merged")
async def get_merged_point_items_by_quotation_id(
    request: Request,
    project_quotation_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """获取合并后的点位列表（按点位名称合并）"""
    service = PointItemConfirmationService(query_db)
    merged_items = await service.get_merged_point_items_by_quotation_id(project_quotation_id)

    return ResponseUtil.success(data=merged_items, msg="获取合并点位列表成功")


@pointItemConfirmationController.put("/batch-confirm-by-name")
@Log(title='点位确认管理', business_type=BusinessType.UPDATE)
async def batch_confirm_point_items_by_name(
    request: Request,
    batch_confirm_dto: BatchPointItemConfirmByNameDTO,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """按点位名称批量确认/取消确认点位"""
    service = PointItemConfirmationService(query_db)
    point_items = await service.batch_confirm_point_items_by_name(
        batch_confirm_dto.project_quotation_id,
        batch_confirm_dto.point_name,
        batch_confirm_dto.confirmed,
        current_user.user.user_name
    )

    action = "确认" if batch_confirm_dto.confirmed else "取消确认"
    return ResponseUtil.success(data=point_items, msg=f"批量{action}点位'{batch_confirm_dto.point_name}'成功")


@pointItemConfirmationController.put("/batch-rename-by-name")
@Log(title='点位确认管理', business_type=BusinessType.UPDATE)
async def batch_rename_point_items_by_name(
    request: Request,
    batch_rename_dto: BatchPointItemRenameByNameDTO,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """按点位名称批量重命名点位"""
    service = PointItemConfirmationService(query_db)
    point_items = await service.batch_rename_point_items_by_name(
        batch_rename_dto.project_quotation_id,
        batch_rename_dto.old_point_name,
        batch_rename_dto.new_point_name,
        current_user.user.user_name
    )

    return ResponseUtil.success(data=point_items, msg=f"批量重命名点位'{batch_rename_dto.old_point_name}'为'{batch_rename_dto.new_point_name}'成功")
