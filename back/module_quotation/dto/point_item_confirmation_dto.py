"""
点位确认相关DTO
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict
from utils.common_util import CamelCaseUtil

to_camel = CamelCaseUtil.snake_to_camel


class PointItemConfirmationDTO(BaseModel):
    """点位确认DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    id: int
    point_name: str
    point_confirmed: bool
    confirmed_by: Optional[str] = None
    confirmed_time: Optional[datetime] = None
    # 添加展示所需的字段
    category: Optional[str] = None
    parameter: Optional[str] = None
    method: Optional[str] = None


class PointItemRenameDTO(BaseModel):
    """点位重命名DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    point_item_id: int
    new_point_name: str


class PointItemConfirmDTO(BaseModel):
    """点位确认DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    point_item_id: int
    confirmed: bool


class BatchPointItemConfirmDTO(BaseModel):
    """批量点位确认DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    point_item_ids: list[int]
    confirmed: bool


class BatchPointItemConfirmByNameDTO(BaseModel):
    """按点位名称批量确认DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    project_quotation_id: int
    point_name: str
    confirmed: bool


class BatchPointItemRenameByNameDTO(BaseModel):
    """按点位名称批量重命名DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    project_quotation_id: int
    old_point_name: str
    new_point_name: str


class MergedPointItemDTO(BaseModel):
    """合并后的点位DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)

    point_name: str
    items: List[PointItemConfirmationDTO]
    total_count: int
    confirmed_count: int
    all_confirmed: bool
    has_unconfirmed: bool
    can_rename: bool


class PointItemConfirmationQueryDTO(BaseModel):
    """点位确认查询DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    project_quotation_id: int
    point_confirmed: Optional[bool] = None
