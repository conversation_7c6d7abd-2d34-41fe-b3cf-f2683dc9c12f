"""
点位确认服务
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from module_quotation.entity.do.project_quotation_item_point_item_do import ProjectQuotationItemPointItem
from module_quotation.dto.point_item_confirmation_dto import (
    PointItemConfirmationDTO,
    PointItemRenameDTO,
    PointItemConfirmDTO,
    BatchPointItemConfirmDTO,
    BatchPointItemConfirmByNameDTO,
    BatchPointItemRenameByNameDTO,
    MergedPointItemDTO,
    PointItemConfirmationQueryDTO
)
from exceptions.exception import ServiceException


class PointItemConfirmationService:
    """点位确认服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_point_items_by_quotation_id(self, project_quotation_id: int, point_confirmed: Optional[bool] = None) -> List[PointItemConfirmationDTO]:
        """根据项目报价ID获取点位明细列表"""
        try:
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.project_quotation_id == project_quotation_id
            )
            
            if point_confirmed is not None:
                stmt = stmt.where(ProjectQuotationItemPointItem.point_confirmed == point_confirmed)
            
            stmt = stmt.order_by(ProjectQuotationItemPointItem.id)
            
            result = await self.db.execute(stmt)
            point_items = result.scalars().all()
            
            return [self._convert_to_confirmation_dto(item) for item in point_items]
            
        except Exception as e:
            raise ServiceException(message=f"获取点位明细列表失败: {str(e)}")
    
    async def rename_point_item(self, point_item_id: int, new_point_name: str, update_by: str) -> PointItemConfirmationDTO:
        """重命名点位"""
        try:
            # 获取点位明细
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.id == point_item_id
            )
            result = await self.db.execute(stmt)
            point_item = result.scalar_one_or_none()
            
            if not point_item:
                raise ServiceException(message="点位明细不存在")
            
            # 更新点位名称
            point_item.point_name = new_point_name
            point_item.update_by = update_by
            point_item.update_time = datetime.now()
            
            await self.db.flush()
            await self.db.commit()
            
            return self._convert_to_confirmation_dto(point_item)
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"重命名点位失败: {str(e)}")
    
    async def confirm_point_item(self, point_item_id: int, confirmed: bool, confirmed_by: str) -> PointItemConfirmationDTO:
        """确认/取消确认点位"""
        try:
            # 获取点位明细
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.id == point_item_id
            )
            result = await self.db.execute(stmt)
            point_item = result.scalar_one_or_none()
            
            if not point_item:
                raise ServiceException(message="点位明细不存在")
            
            # 更新确认状态
            point_item.point_confirmed = confirmed
            point_item.confirmed_by = confirmed_by if confirmed else None
            point_item.confirmed_time = datetime.now() if confirmed else None
            point_item.update_by = confirmed_by
            point_item.update_time = datetime.now()
            
            await self.db.flush()
            await self.db.commit()
            
            return self._convert_to_confirmation_dto(point_item)
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"确认点位失败: {str(e)}")
    
    async def batch_confirm_point_items(self, point_item_ids: List[int], confirmed: bool, confirmed_by: str) -> List[PointItemConfirmationDTO]:
        """批量确认/取消确认点位"""
        try:
            # 批量更新确认状态
            stmt = update(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.id.in_(point_item_ids)
            ).values(
                point_confirmed=confirmed,
                confirmed_by=confirmed_by if confirmed else None,
                confirmed_time=datetime.now() if confirmed else None,
                update_by=confirmed_by,
                update_time=datetime.now()
            )
            
            await self.db.execute(stmt)
            await self.db.commit()
            
            # 获取更新后的点位明细
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.id.in_(point_item_ids)
            )
            result = await self.db.execute(stmt)
            point_items = result.scalars().all()
            
            return [self._convert_to_confirmation_dto(item) for item in point_items]
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"批量确认点位失败: {str(e)}")
    
    async def get_point_item_by_id(self, point_item_id: int) -> Optional[PointItemConfirmationDTO]:
        """根据ID获取点位明细"""
        try:
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.id == point_item_id
            )
            result = await self.db.execute(stmt)
            point_item = result.scalar_one_or_none()
            
            if point_item:
                return self._convert_to_confirmation_dto(point_item)
            return None
            
        except Exception as e:
            raise ServiceException(message=f"获取点位明细失败: {str(e)}")
    
    async def get_merged_point_items_by_quotation_id(self, project_quotation_id: int) -> List[MergedPointItemDTO]:
        """获取合并后的点位列表（按点位名称合并）"""
        try:
            # 获取所有点位明细
            point_items = await self.get_point_items_by_quotation_id(project_quotation_id)

            # 按点位名称分组
            point_groups = {}
            for item in point_items:
                point_name = item.point_name
                if point_name not in point_groups:
                    point_groups[point_name] = []
                point_groups[point_name].append(item)

            # 构建合并后的点位数据
            merged_items = []
            for point_name, items in point_groups.items():
                confirmed_count = sum(1 for item in items if item.point_confirmed)
                total_count = len(items)
                all_confirmed = confirmed_count == total_count
                has_unconfirmed = confirmed_count < total_count
                can_rename = confirmed_count == 0  # 只有全部未确认才能重命名

                merged_item = MergedPointItemDTO(
                    point_name=point_name,
                    items=items,
                    total_count=total_count,
                    confirmed_count=confirmed_count,
                    all_confirmed=all_confirmed,
                    has_unconfirmed=has_unconfirmed,
                    can_rename=can_rename
                )
                merged_items.append(merged_item)

            # 按点位名称排序
            merged_items.sort(key=lambda x: x.point_name)
            return merged_items

        except Exception as e:
            raise ServiceException(message=f"获取合并点位列表失败: {str(e)}")

    async def batch_confirm_point_items_by_name(self, project_quotation_id: int, point_name: str, confirmed: bool, confirmed_by: str) -> List[PointItemConfirmationDTO]:
        """按点位名称批量确认/取消确认点位"""
        try:
            # 获取指定点位名称的所有点位明细ID
            stmt = select(ProjectQuotationItemPointItem.id).where(
                ProjectQuotationItemPointItem.project_quotation_id == project_quotation_id,
                ProjectQuotationItemPointItem.point_name == point_name
            )
            result = await self.db.execute(stmt)
            point_item_ids = [row[0] for row in result.fetchall()]

            if not point_item_ids:
                raise ServiceException(message=f"未找到点位名称为'{point_name}'的点位明细")

            # 调用现有的批量确认方法
            return await self.batch_confirm_point_items(point_item_ids, confirmed, confirmed_by)

        except Exception as e:
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"按点位名称批量确认失败: {str(e)}")

    async def batch_rename_point_items_by_name(self, project_quotation_id: int, old_point_name: str, new_point_name: str, update_by: str) -> List[PointItemConfirmationDTO]:
        """按点位名称批量重命名点位"""
        try:
            # 检查是否有已确认的点位
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.project_quotation_id == project_quotation_id,
                ProjectQuotationItemPointItem.point_name == old_point_name,
                ProjectQuotationItemPointItem.point_confirmed == True
            )
            result = await self.db.execute(stmt)
            confirmed_items = result.scalars().all()

            if confirmed_items:
                raise ServiceException(message=f"点位'{old_point_name}'中有已确认的明细，无法重命名")

            # 批量更新点位名称
            stmt = update(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.project_quotation_id == project_quotation_id,
                ProjectQuotationItemPointItem.point_name == old_point_name
            ).values(
                point_name=new_point_name,
                update_by=update_by,
                update_time=datetime.now()
            )

            await self.db.execute(stmt)
            await self.db.commit()

            # 获取更新后的点位明细
            stmt = select(ProjectQuotationItemPointItem).where(
                ProjectQuotationItemPointItem.project_quotation_id == project_quotation_id,
                ProjectQuotationItemPointItem.point_name == new_point_name
            )
            result = await self.db.execute(stmt)
            updated_items = result.scalars().all()

            return [self._convert_to_confirmation_dto(item) for item in updated_items]

        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"按点位名称批量重命名失败: {str(e)}")

    def _convert_to_confirmation_dto(self, point_item: ProjectQuotationItemPointItem) -> PointItemConfirmationDTO:
        """转换为确认DTO"""
        return PointItemConfirmationDTO(
            id=point_item.id,
            point_name=point_item.point_name,
            point_confirmed=point_item.point_confirmed or False,
            confirmed_by=point_item.confirmed_by,
            confirmed_time=point_item.confirmed_time,
            category=point_item.category,
            parameter=point_item.parameter,
            method=point_item.method
        )
