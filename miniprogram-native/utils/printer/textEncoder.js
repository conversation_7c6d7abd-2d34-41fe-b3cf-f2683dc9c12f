/**
 * 中文编码处理模块
 * 解决小程序蓝牙打印中文乱码问题
 * 支持中文字符、全角字符、特殊符号等
 */

class ChineseTextEncoder {
  constructor() {
    // 初始化字符编码映射
    this.initCharacterMaps();
  }

  /**
   * 初始化字符编码映射表
   */
  initCharacterMaps() {
    // 常用中文字符的GBK编码映射
    this.chineseMap = {
      // 基本汉字
      '样': [0xD1, 0xF9], '品': [0xC6, 0xB7], '类': [0xC0, 0xE0], '别': [0xB1, 0xF0],
      '编': [0xB1, 0xE0], '号': [0xBA, 0xC5], '采': [0xB2, 0xC9], '日': [0xC8, 0xD5],
      '期': [0xC6, 0xDA], '点': [0xB5, 0xE3], '位': [0xCE, 0xBB], '检': [0xBC, 0xEC],
      '测': [0xB2, 0xE2], '项': [0xCF, 0xEE], '目': [0xC4, 0xBF], '保': [0xB1, 0xA3],
      '存': [0xB4, 0xE6], '容': [0xC8, 0xDD], '器': [0xC6, 0xF7], '方': [0xB7, 0xBD],
      '式': [0xCA, 0xBD], '状': [0xD7, 0xB4], '态': [0xCC, 0xAC], '待': [0xB4, 0xFD],
      '合': [0xBA, 0xCF], '格': [0xB8, 0xF1], '不': [0xB2, 0xBB], '打': [0xB4, 0xF2],
      '印': [0xD3, 0xA1], '试': [0xCA, 0xD4], '设': [0xC9, 0xE8], '备': [0xB1, 0xB8],
      '时': [0xCA, 0xB1], '间': [0xBC, 0xE4], '纸': [0xD6, 0xBD], '张': [0xD5, 0xC5],
      '系': [0xCF, 0xB5], '统': [0xCD, 0xB3], '成': [0xB3, 0xC9], '功': [0xB9, 0xA6],
      '水': [0xCB, 0xAE], '土': [0xCD, 0xC1], '气': [0xC6, 0xF8], '食': [0xCA, 0xB3],
      '周': [0xD6, 0xDC], '瓶': [0xC6, 0xBF], '组': [0xD7, 0xE9], '常': [0xB3, 0xA3],
      '规': [0xB9, 0xE6], '温': [0xCE, 0xC2], '塑': [0xCB, 0xDC], '料': [0xC1, 0xCF],
      '毫': [0xBA, 0xC1], '升': [0xC9, 0xFD], '冷': [0xC0, 0xE4], '藏': [0xB2, 0xD8],
      '冻': [0xB6, 0xB3], '金': [0xBD, 0xF0], '属': [0xCA, 0xF4], '重': [0xD6, 0xD8],
      '化': [0xBB, 0xAF], '学': [0xD1, 0xA7], '物': [0xCE, 0xEF], '理': [0xC0, 0xED],
      '生': [0xC9, 0xFA], '微': [0xCE, 0xA2], '菌': [0xBE, 0xFA], '环': [0xBB, 0xB7],
      '境': [0xBE, 0xB3], '监': [0xBC, 0xE0], '管': [0xB9, 0xDC], '实': [0xCA, 0xB5],
      '验': [0xD1, 0xE9], '室': [0xCA, 0xD2], '信': [0xD0, 0xC5], '息': [0xCF, 0xA2],
      '标': [0xB1, 0xEA], '签': [0xC7, 0xA9], '报': [0xB1, 0xA8], '告': [0xB8, 0xE6],
      '结': [0xBD, 0xE1], '果': [0xB9, 0xFB], '数': [0xCA, 0xFD], '据': [0xBE, 0xDD],
      '分': [0xB7, 0xD6], '析': [0xCE, 0xF6], '质': [0xD6, 0xCA], '量': [0xC1, 0xBF],
      '控': [0xBF, 0xD8], '制': [0xD6, 0xC6], '准': [0xD7, 0xBC], '操': [0xB2, 0xD9],
      '作': [0xD7, 0xF7], '流': [0xC1, 0xF7], '程': [0xB3, 0xCC], '记': [0xBC, 0xC7],
      '录': [0xC2, 0xBC], '档': [0xB5, 0xB5], '案': [0xB0, 0xB8], '查': [0xB2, 0xE9],
      '询': [0xD1, 0xAF], '计': [0xBC, 0xC6], '表': [0xB1, 0xED], '导': [0xB5, 0xBC],
      '出': [0xB3, 0xF6], '入': [0xC8, 0xEB], '库': [0xBF, 0xE2], '取': [0xC8, 0xA1],
      '删': [0xC9, 0xBE], '除': [0xB3, 0xFD], '修': [0xD0, 0xDE], '改': [0xB8, 0xC4],
      '新': [0xD0, 0xC2], '增': [0xD4, 0xF6], '加': [0xBC, 0xD3], '更': [0xB8, 0xFC],
      '版': [0xB0, 0xE6], '本': [0xB1, 0xBE], '用': [0xD3, 0xC3], '户': [0xBB, 0xA7],
      '权': [0xC8, 0xA8], '限': [0xCF, 0xDE], '登': [0xB5, 0xC7], '退': [0xCD, 0xCB],
      '密': [0xC3, 0xDC], '码': [0xC2, 0xEB], '安': [0xB0, 0xB2], '全': [0xC8, 0xAB],
      '配': [0xC5, 0xE4], '置': [0xD6, 0xC3], '参': [0xB2, 0xCE], '选': [0xD1, 0xA1],
      '定': [0xB6, 0xA8], '确': [0xC8, 0xB7], '认': [0xC8, 0xCF], '消': [0xCF, 0xFB],
      '应': [0xD3, 0xA6], '帮': [0xB0, 0xEF], '助': [0xD6, 0xFA], '关': [0xB9, 0xD8],
      '于': [0xD3, 0xDA], '联': [0xC1, 0xAA], '我': [0xCE, 0xD2], '们': [0xC3, 0xC7],
      '公': [0xB9, 0xAB], '司': [0xCB, 0xBE], '地': [0xB5, 0xD8], '址': [0xD6, 0xB7],
      '电': [0xB5, 0xE7], '话': [0xBB, 0xB0], '传': [0xB4, 0xAB], '真': [0xD5, 0xE6],
      '邮': [0xD3, 0xCA], '箱': [0xCF, 0xE4], '网': [0xCD, 0xF8], '站': [0xD5, 0xBE],
      '服': [0xB7, 0xFE], '务': [0xCE, 0xF1], '支': [0xD6, 0xA7], '持': [0xB3, 0xD6],
      '技': [0xBC, 0xBC], '术': [0xCA, 0xF5], '开': [0xBF, 0xAA], '发': [0xB7, 0xA2],
      '维': [0xCE, 0xAC], '护': [0xBB, 0xA4], '级': [0xBC, 0xB6], '优': [0xD3, 0xC5],
      '进': [0xBD, 0xF8], '完': [0xCD, 0xEA], '善': [0xC9, 0xC6], '提': [0xCC, 0xE1],
      '高': [0xB8, 0xDF], '效': [0xD0, 0xA7], '率': [0xC2, 0xCA], '稳': [0xCE, 0xC8],
      '可': [0xBF, 0xC9], '靠': [0xBF, 0xBF], '快': [0xBF, 0xEC], '速': [0xCB, 0xD9],
      '便': [0xB1, 0xE3], '捷': [0xBD, 0xDD], '简': [0xBC, 0xF2], '单': [0xB5, 0xA5],
      '易': [0xD2, 0xD7], '智': [0xD6, 0xC7], '能': [0xC4, 0xDC], '自': [0xD7, 0xD4],
      '动': [0xB6, 0xAF], '手': [0xCA, 0xD6], '工': [0xB9, 0xA4], '中': [0xD6, 0xD0],
      '文': [0xCE, 0xC4], '英': [0xD3, 0xA2], '语': [0xD3, 0xEF], '字': [0xD7, 0xD6],
      '符': [0xB7, 0xFB], '特': [0xCC, 0xD8], '殊': [0xCA, 0xE2], '的': [0xB5, 0xC4],
      '是': [0xCA, 0xC7], '有': [0xD3, 0xD0], '在': [0xD4, 0xDA], '了': [0xC1, 0xCB],
      '和': [0xBA, 0xCD], '个': [0xB8, 0xF6], '人': [0xC8, 0xCB], '这': [0xD5, 0xE2],
      '上': [0xC9, 0xCF], '来': [0xC0, 0xB4], '到': [0xB5, 0xBD], '大': [0xB4, 0xF3],
      '地': [0xB5, 0xD8], '为': [0xCE, 0xAA], '子': [0xD7, 0xD3], '就': [0xBE, 0xCD],
      '民': [0xC3, 0xF1], '以': [0xD2, 0xD4], '会': [0xBB, 0xE1], '家': [0xBC, 0xD2],
      '下': [0xCF, 0xC2], '而': [0xB6, 0xF8], '过': [0xB9, 0xFD], '天': [0xCC, 0xEC],
      '去': [0xC8, 0xA5], '对': [0xB6, 0xD4], '小': [0xD0, 0xA1], '多': [0xB6, 0xE0],
      '然': [0xC8, 0xBB], '心': [0xD0, 0xC4], '么': [0xC3, 0xB4], '之': [0xD6, 0xAE],
      '都': [0xB6, 0xBC], '好': [0xBA, 0xC3], '看': [0xBF, 0xB4], '起': [0xC6, 0xF0],
      '当': [0xB5, 0xB1], '没': [0xC3, 0xBB], '只': [0xD6, 0xBB], '如': [0xC8, 0xE7],
      '事': [0xCA, 0xC2], '把': [0xB0, 0xD1], '还': [0xBB, 0xB9], '第': [0xB5, 0xDA],
      '道': [0xB5, 0xC0], '想': [0xCF, 0xEB], '种': [0xD6, 0xD6], '美': [0xC3, 0xC0],
      '总': [0xD7, 0xDC], '从': [0xB4, 0xD3], '无': [0xCE, 0xDE], '情': [0xC7, 0xE9],
      '己': [0xBC, 0xBA], '面': [0xC3, 0xE6], '最': [0xD7, 0xEE], '女': [0xC5, 0xAE],
      '但': [0xB5, 0xAB], '现': [0xCF, 0xD6], '前': [0xC7, 0xB0], '些': [0xD0, 0xA9],
      '所': [0xCB, 0xF9], '同': [0xCD, 0xAC], '又': [0xD3, 0xD6], '行': [0xD0, 0xD0],
      '意': [0xD2, 0xE2], '它': [0xCB, 0xFC], '头': [0xCD, 0xB7], '经': [0xBE, 0xAD],
      '长': [0xB3, 0xA4], '儿': [0xB6, 0xF9], '回': [0xBB, 0xD8], '爱': [0xB0, 0xAE],
      '老': [0xC0, 0xCF], '因': [0xD2, 0xF2], '很': [0xBA, 0xDC], '给': [0xB8, 0xF8],
      '名': [0xC3, 0xFB], '法': [0xB7, 0xA8], '斯': [0xCB, 0xB9], '知': [0xD6, 0xAA],
      '世': [0xCA, 0xC0], '什': [0xCA, 0xB2], '两': [0xC1, 0xBD], '次': [0xB4, 0xCE],
      '使': [0xCA, 0xB9], '身': [0xC9, 0xED], '者': [0xD5, 0xDF], '被': [0xB1, 0xBB],
      '已': [0xD2, 0xD1], '亲': [0xC7, 0xD7], '其': [0xC6, 0xE4], '此': [0xB4, 0xCB],
      '常': [0xB3, 0xA3], '与': [0xD3, 0xEB], '活': [0xBB, 0xEE], '正': [0xD5, 0xFD],
      '感': [0xB8, 0xD0], '见': [0xBC, 0xFB], '明': [0xC3, 0xF7], '问': [0xCE, 0xCA],
      '力': [0xC1, 0xA6], '尔': [0xB6, 0xFB], '几': [0xBC, 0xB8], '外': [0xCD, 0xE2],
      '孩': [0xBA, 0xA2], '相': [0xCF, 0xE0], '西': [0xCE, 0xF7], '走': [0xD7, 0xDF],
      '将': [0xBD, 0xAB], '月': [0xD4, 0xC2], '十': [0xCA, 0xAE], '向': [0xCF, 0xF2],
      '声': [0xC9, 0xF9], '车': [0xB3, 0xB5], '三': [0xC8, 0xFD], '机': [0xBB, 0xFA],
      '每': [0xC3, 0xBF], '并': [0xB2, 0xA2], '太': [0xCC, 0xAB], '比': [0xB1, 0xC8],
      '才': [0xB2, 0xC5], '夫': [0xB7, 0xF2], '再': [0xD4, 0xD9], '书': [0xCA, 0xE9],
      '部': [0xB2, 0xBF], '风': [0xB7, 0xE7], '呢': [0xC4, 0xD8], '今': [0xBD, 0xF1],
      '资': [0xD7, 0xCA], '度': [0xB6, 0xC8], '体': [0xCC, 0xE5], '员': [0xD4, 0xB1],
      '呀': [0xD1, 0xC7], '求': [0xC7, 0xF3], '静': [0xBE, 0xB2], '团': [0xCD, 0xC5],
      '色': [0xC9, 0xAB], '先': [0xCF, 0xC8], '含': [0xBA, 0xAC], '红': [0xBA, 0xEC],
      '四': [0xCB, 0xC4], '或': [0xBB, 0xF2], '七': [0xC6, 0xDF], '八': [0xB0, 0xCB],
      '华': [0xBB, 0xAA], '跟': [0xB8, 0xFA], '历': [0xC0, 0xFA], '农': [0xC5, 0xA9],
      '百': [0xB0, 0xD9], '谈': [0xCC, 0xB8], '拿': [0xC4, 0xC3], '条': [0xCC, 0xF5],
      '男': [0xC4, 0xD0], '望': [0xCD, 0xFB], '听': [0xCC, 0xFD], '白': [0xB0, 0xD7],
      '该': [0xB8, 0xC3], '南': [0xC4, 0xCF], '千': [0xC7, 0xA7], '争': [0xD5, 0xF9],
      '误': [0xCE, 0xF3], '草': [0xB2, 0xDD], '何': [0xBA, 0xCE], '观': [0xB9, 0xDB],
      '指': [0xD6, 0xB8], '委': [0xCE, 0xAF], '光': [0xB9, 0xE2], '变': [0xB1, 0xE4],
      '社': [0xC9, 0xE7], '早': [0xD4, 0xE7], '曾': [0xD4, 0xF8], '找': [0xD5, 0xD2],
      '装': [0xD7, 0xB0], '显': [0xCF, 0xD4], '吧': [0xB0, 0xC9], '阿': [0xB0, 0xA2],
      '李': [0xC0, 0xEE], '谁': [0xCB, 0xAD], '造': [0xD4, 0xEC], '嘛': [0xC2, 0xEF],
      '拉': [0xC0, 0xAD], '校': [0xD0, 0xA3], '医': [0xD2, 0xBD], '院': [0xD4, 0xBA],
      '始': [0xCA, 0xBC], '立': [0xC1, 0xA2], '较': [0xBD, 0xCF], '六': [0xC1, 0xF9],
      '少': [0xC9, 0xD9], '马': [0xC2, 0xED], '内': [0xC4, 0xDA], '决': [0xBE, 0xF6],
      '建': [0xBD, 0xA8], '领': [0xC1, 0xEC], '思': [0xCB, 0xBC], '代': [0xB4, 0xFA],
      '住': [0xD7, 0xA1], '派': [0xC5, 0xC9], '音': [0xD2, 0xF4], '收': [0xCA, 0xD5],
      '毛': [0xC3, 0xAB], '纪': [0xBC, 0xCD], '破': [0xC6, 0xC6], '专': [0xD7, 0xA8],
      '围': [0xCE, 0xA7], '注': [0xD7, 0xA2], '远': [0xD4, 0xB6], '材': [0xB2, 0xC4],
      '排': [0xC5, 0xC5], '供': [0xB9, 0xA9], '河': [0xBA, 0xD3], '封': [0xB7, 0xE2],
      '另': [0xC1, 0xED], '施': [0xCA, 0xA9], '减': [0xBC, 0xF5], '树': [0xCA, 0xF7],
      '溶': [0xC8, 0xDC], '怎': [0xD4, 0xF5], '止': [0xD6, 0xB9], '言': [0xD1, 0xD4],
      '士': [0xCA, 0xBF], '均': [0xBE, 0xF9], '武': [0xCE, 0xE4], '固': [0xB9, 0xCC],
      '叶': [0xD2, 0xB6], '鱼': [0xD3, 0xE3], '波': [0xB2, 0xA8], '视': [0xCA, 0xD3],
      '仅': [0xBD, 0xF6], '费': [0xB7, 0xD1], '紧': [0xBD, 0xF4], '左': [0xD7, 0xF3],
      '章': [0xD5, 0xC2], '朝': [0xB3, 0xAF], '害': [0xBA, 0xA6], '续': [0xD0, 0xF8],
      '轻': [0xC7, 0xE1], '充': [0xB3, 0xE4], '兵': [0xB1, 0xF8], '源': [0xD4, 0xB4],
      '判': [0xC5, 0xD0], '足': [0xD7, 0xE3], '某': [0xC4, 0xB3], '练': [0xC1, 0xB7],
      '差': [0xB2, 0xEE], '致': [0xD6, 0xC2], '板': [0xB0, 0xE5], '田': [0xCC, 0xEF],
      '降': [0xBD, 0xB5], '黑': [0xBA, 0xDA], '犯': [0xB7, 0xB8], '负': [0xB8, 0xBA],
      '击': [0xBB, 0xF7], '范': [0xB7, 0xB6], '继': [0xBC, 0xCC], '兴': [0xD0, 0xCB],
      '似': [0xCB, 0xC6], '余': [0xD3, 0xE0], '坚': [0xBC, 0xE1], '曲': [0xC7, 0xFA],
      '输': [0xCA, 0xE4], '故': [0xB9, 0xCA], '城': [0xB3, 0xC7], '够': [0xB9, 0xBB],
      '送': [0xCB, 0xCD], '笔': [0xB1, 0xCA], '船': [0xB4, 0xAC], '占': [0xD5, 0xBC],
      '右': [0xD3, 0xD2], '财': [0xB2, 0xC6], '吃': [0xB3, 0xD4], '富': [0xB8, 0xBB],
      '春': [0xB4, 0xBA], '职': [0xD6, 0xB0], '觉': [0xBE, 0xF5], '汉': [0xBA, 0xBA],
      '画': [0xBB, 0xAD], '巴': [0xB0, 0xCD], '跑': [0xC5, 0xDC], '趣': [0xC8, 0xA4],
      '份': [0xB7, 0xDD], '米': [0xC3, 0xD7], '整': [0xD5, 0xFB], '君': [0xBE, 0xFD],
      '木': [0xC4, 0xBE], '五': [0xCE, 0xE5], '克': [0xBF, 0xCB], '原': [0xD4, 0xAD],
      '强': [0xC7, 0xBF], '放': [0xB7, 0xC5], '难': [0xC4, 0xD1], '火': [0xBB, 0xF0],
      '满': [0xC2, 0xFA], '血': [0xD1, 0xAA], '接': [0xBD, 0xD3], '群': [0xC8, 0xBA],
      '斗': [0xB6, 0xB7], '居': [0xBE, 0xD3], '证': [0xD6, 0xA4], '育': [0xD3, 0xFD],
      '呼': [0xBA, 0xF4], '异': [0xD2, 0xEC], '洲': [0xD6, 0xDE], '刻': [0xBF, 0xCC],
      '云': [0xD4, 0xC6], '毕': [0xB1, 0xCF], '治': [0xD6, 0xCE], '秒': [0xC3, 0xEB],
      '睛': [0xBE, 0xA6], '布': [0xB2, 0xBC], '夏': [0xCF, 0xC4], '忙': [0xC3, 0xA6],
      '投': [0xCD, 0xB6], '坐': [0xD7, 0xF8], '肯': [0xBF, 0xCF], '妻': [0xC6, 0xDE],
      '台': [0xCC, 0xA8], '州': [0xD6, 0xDD], '东': [0xB6, 0xAB], '季': [0xBC, 0xBE],
      '转': [0xD7, 0xAA], '灵': [0xC1, 0xE9], '象': [0xCF, 0xF3], '王': [0xCD, 0xF5],
      '志': [0xD6, 0xBE], '达': [0xB4, 0xEF], '忽': [0xBA, 0xF6], '海': [0xBA, 0xA3],
      '角': [0xBD, 0xC7], '局': [0xBE, 0xD6], '师': [0xCA, 0xA6], '否': [0xB7, 0xF1],
      '臣': [0xB3, 0xBC], '江': [0xBD, 0xAD], '湖': [0xBA, 0xFE], '怕': [0xC5, 0xC2],
      '讲': [0xBD, 0xB2], '界': [0xBD, 0xE7], '政': [0xD5, 0xFE], '府': [0xB8, 0xAE],
      '宅': [0xD5, 0xAC], '络': [0xC2, 0xE7], '客': [0xBF, 0xCD], '危': [0xCE, 0xA3],
      '旗': [0xC6, 0xEC], '钱': [0xC7, 0xAE], '写': [0xD0, 0xB4], '离': [0xC0, 0xEB],
      '病': [0xB2, 0xA1], '影': [0xD3, 0xB0], '叫': [0xBD, 0xD0], '桌': [0xD7, 0xC0],
      '党': [0xB5, 0xB3], '商': [0xC9, 0xCC], '队': [0xB6, 0xD3], '县': [0xCF, 0xD8],
      '压': [0xD1, 0xB9], '形': [0xD0, 0xCE], '识': [0xCA, 0xB6], '运': [0xD4, 0xCB],
      '块': [0xBF, 0xE9], '革': [0xB8, 0xEF], '命': [0xC3, 0xFC], '解': [0xBD, 0xE2],
      '汝': [0xC8, 0xEA], '损': [0xCB, 0xF0], '昌': [0xB2, 0xFD], '浪': [0xC0, 0xCB],
      '跳': [0xCC, 0xF8], '贵': [0xB9, 0xF3], '牛': [0xC5, 0xA3], '产': [0xB2, 0xFA],
      '婚': [0xBB, 0xE9], '随': [0xCB, 0xE6], '粮': [0xC1, 0xB8], '企': [0xC6, 0xF3],
      '业': [0xD2, 0xB5], '义': [0xD2, 0xE5], '兄': [0xD0, 0xD6], '弟': [0xB5, 0xDC],
      '玉': [0xD3, 0xF1], '忆': [0xD2, 0xE4], '招': [0xD5, 0xD0], '顺': [0xCB, 0xB3],
      '科': [0xBF, 0xC6], '推': [0xCD, 0xC6], '刚': [0xB8, 0xD5], '妨': [0xB7, 0xC1],
      '友': [0xD3, 0xD1], '床': [0xB4, 0xB2], '承': [0xB3, 0xD0], '乱': [0xC2, 0xD2],
      '永': [0xD3, 0xC0], '拜': [0xB0, 0xDD],

      // 补充常用字符（解决乱码问题）
      '瓶': [0xC6, 0xBF], '组': [0xD7, 0xE9], '管': [0xB9, 0xDC], '理': [0xC0, 0xED],
      '系': [0xCF, 0xB5], '统': [0xCD, 0xB3], '实': [0xCA, 0xB5], '验': [0xD1, 0xE9],
      '室': [0xCA, 0xD2], '信': [0xD0, 0xC5], '息': [0xCF, 0xA2], '标': [0xB1, 0xEA],
      '签': [0xC7, 0xA9], '打': [0xB4, 0xF2], '印': [0xD3, 0xA1], '机': [0xBB, 0xFA],
      '配': [0xC5, 0xE4], '置': [0xD6, 0xC3], '连': [0xC1, 0xAC], '接': [0xBD, 0xD3],
      '成': [0xB3, 0xC9], '功': [0xB9, 0xA6], '失': [0xCA, 0xA7], '败': [0xB0, 0xDC],
      '搜': [0xCB, 0xD1], '索': [0xCB, 0xF7], '设': [0xC9, 0xE8], '备': [0xB1, 0xB8],
      '蓝': [0xC0, 0xB6], '牙': [0xD1, 0xC0], '传': [0xB4, 0xAB], '输': [0xCA, 0xE4],
      '进': [0xBD, 0xF8], '度': [0xB6, 0xC8], '完': [0xCD, 0xEA], '成': [0xB3, 0xC9],
      '错': [0xB4, 0xED], '误': [0xCE, 0xF3], '重': [0xD6, 0xD8], '试': [0xCA, 0xD4],
      '次': [0xB4, 0xCE], '数': [0xCA, 0xFD], '超': [0xB3, 0xAC], '过': [0xB9, 0xFD],
      '限': [0xCF, 0xDE], '制': [0xD6, 0xC6], '请': [0xC7, 0xEB], '检': [0xBC, 0xEC],
      '查': [0xB2, 0xE9], '网': [0xCD, 0xF8], '络': [0xC2, 0xE7], '状': [0xD7, 0xB4],
      '态': [0xCC, 0xAC], '或': [0xBB, 0xF2], '联': [0xC1, 0xAA], '系': [0xCF, 0xB5],
      '技': [0xBC, 0xBC], '术': [0xCA, 0xF5], '支': [0xD6, 0xA7], '持': [0xB3, 0xD6],
      '人': [0xC8, 0xCB], '员': [0xD4, 0xB1], '处': [0xB4, 0xA6], '理': [0xC0, 0xED],
      '中': [0xD6, 0xD0], '文': [0xCE, 0xC4], '字': [0xD7, 0xD6], '符': [0xB7, 0xFB],
      '编': [0xB1, 0xE0], '码': [0xC2, 0xEB], '格': [0xB8, 0xF1], '式': [0xCA, 0xBD],
      '化': [0xBB, 0xAF], '对': [0xB6, 0xD4], '齐': [0xC6, 0xEB], '左': [0xD7, 0xF3],
      '右': [0xD3, 0xD2], '居': [0xBE, 0xD3], '截': [0xBD, 0xD8], '取': [0xC8, 0xA1],
      '宽': [0xBF, 0xED], '显': [0xCF, 0xD4], '示': [0xCA, 0xBE], '计': [0xBC, 0xC6],
      '算': [0xCB, 0xE3], '混': [0xBB, 0xEC], '排': [0xC5, 0xC5], '英': [0xD3, 0xA2],
      '包': [0xB0, 0xFC], '含': [0xBA, 0xAC], '特': [0xCC, 0xD8], '殊': [0xCA, 0xE2],
      '符': [0xB7, 0xFB], '号': [0xBA, 0xC5], '复': [0xB8, 0xB4], '选': [0xD1, 0xA1],
      '框': [0xBF, 0xF2], '单': [0xB5, 0xA5], '位': [0xCE, 0xBB], '箭': [0xBC, 0xFD],
      '头': [0xCD, 0xB7], '数': [0xCA, 0xFD], '学': [0xD1, 0xA7], '货': [0xBB, 0xF5],
      '币': [0xB1, 0xD2], '运': [0xD4, 0xCB], '算': [0xCB, 0xE3], '标': [0xB1, 0xEA],
      '点': [0xB5, 0xE3], '制': [0xD6, 0xC6], '表': [0xB1, 0xED], '线': [0xCF, 0xDF],
      '框': [0xBF, 0xF2], '架': [0xBC, 0xDC], '图': [0xCD, 0xBC], '形': [0xD0, 0xCE],
      '星': [0xD0, 0xC7], '注': [0xD7, 0xA2], '释': [0xCA, 0xCD], '其': [0xC6, 0xE4],
      '他': [0xCB, 0xFB], '常': [0xB3, 0xA3], '用': [0xD3, 0xC3], '汉': [0xBA, 0xBA],

      // 更多常用字符
      '牙': [0xD1, 0xC0], '蓝': [0xC0, 0xB6], '印': [0xD3, 0xA1], '机': [0xBB, 0xFA],
      '置': [0xD6, 0xC3], '配': [0xC5, 0xE4], '连': [0xC1, 0xAC], '接': [0xBD, 0xD3],
      '搜': [0xCB, 0xD1], '索': [0xCB, 0xF7], '设': [0xC9, 0xE8], '备': [0xB1, 0xB8],
      '列': [0xC1, 0xD0], '表': [0xB1, 0xED], '选': [0xD1, 0xA1], '择': [0xD4, 0xF1],
      '等': [0xB5, 0xC8], '待': [0xB4, 0xFD], '连': [0xC1, 0xAC], '接': [0xBD, 0xD3],
      '成': [0xB3, 0xC9], '功': [0xB9, 0xA6], '提': [0xCC, 0xE1], '示': [0xCA, 0xBE],
      '核': [0xBA, 0xCB], '心': [0xD0, 0xC4], '问': [0xCE, 0xCA], '题': [0xCC, 0xE2],
      '测': [0xB2, 0xE2], '试': [0xCA, 0xD4], '确': [0xC8, 0xB7], '认': [0xC8, 0xCF],
      '对': [0xB6, 0xD4], '话': [0xBB, 0xB0], '框': [0xBF, 0xF2], '点': [0xB5, 0xE3],
      '击': [0xBB, 0xF7], '定': [0xB6, 0xA8], '观': [0xB9, 0xDB], '察': [0xB2, 0xEC],
      '进': [0xBD, 0xF8], '度': [0xB6, 0xC8], '显': [0xCF, 0xD4], '示': [0xCA, 0xBE],
      '验': [0xD1, 0xE9], '证': [0xD6, 0xA4], '分': [0xB7, 0xD6], '包': [0xB0, 0xFC],
      '功': [0xB9, 0xA6], '能': [0xC4, 0xDC], '等': [0xB5, 0xC8], '完': [0xCD, 0xEA],
      '打': [0xB4, 0xF2], '印': [0xD3, 0xA1], '出': [0xB3, 0xF6], '来': [0xC0, 0xB4],

      // 补充缺失字符
      '湿': [0xCA, 0xAA], '度': [0xB6, 0xC8], '％': [0xA1, 0xEA], '温': [0xCE, 0xC2],
      '℃': [0xA1, 0xE9], '牙': [0xD1, 0xC0], '蓝': [0xC0, 0xB6], '机': [0xBB, 0xFA],
      '置': [0xD6, 0xC3], '配': [0xC5, 0xE4], '进': [0xBD, 0xF8], '显': [0xCF, 0xD4],
      '验': [0xD1, 0xE9], '分': [0xB7, 0xD6], '包': [0xB0, 0xFC], '功': [0xB9, 0xA6]
    };

    // 全角字符映射
    this.fullWidthMap = {
      // 全角数字 (０-９)
      '０': [0xA3, 0xB0], '１': [0xA3, 0xB1], '２': [0xA3, 0xB2], '３': [0xA3, 0xB3],
      '４': [0xA3, 0xB4], '５': [0xA3, 0xB5], '６': [0xA3, 0xB6], '７': [0xA3, 0xB7],
      '８': [0xA3, 0xB8], '９': [0xA3, 0xB9],

      // 全角英文字母 (Ａ-Ｚ, ａ-ｚ)
      'Ａ': [0xA3, 0xC1], 'Ｂ': [0xA3, 0xC2], 'Ｃ': [0xA3, 0xC3], 'Ｄ': [0xA3, 0xC4],
      'Ｅ': [0xA3, 0xC5], 'Ｆ': [0xA3, 0xC6], 'Ｇ': [0xA3, 0xC7], 'Ｈ': [0xA3, 0xC8],
      'Ｉ': [0xA3, 0xC9], 'Ｊ': [0xA3, 0xCA], 'Ｋ': [0xA3, 0xCB], 'Ｌ': [0xA3, 0xCC],
      'Ｍ': [0xA3, 0xCD], 'Ｎ': [0xA3, 0xCE], 'Ｏ': [0xA3, 0xCF], 'Ｐ': [0xA3, 0xD0],
      'Ｑ': [0xA3, 0xD1], 'Ｒ': [0xA3, 0xD2], 'Ｓ': [0xA3, 0xD3], 'Ｔ': [0xA3, 0xD4],
      'Ｕ': [0xA3, 0xD5], 'Ｖ': [0xA3, 0xD6], 'Ｗ': [0xA3, 0xD7], 'Ｘ': [0xA3, 0xD8],
      'Ｙ': [0xA3, 0xD9], 'Ｚ': [0xA3, 0xDA],

      'ａ': [0xA3, 0xE1], 'ｂ': [0xA3, 0xE2], 'ｃ': [0xA3, 0xE3], 'ｄ': [0xA3, 0xE4],
      'ｅ': [0xA3, 0xE5], 'ｆ': [0xA3, 0xE6], 'ｇ': [0xA3, 0xE7], 'ｈ': [0xA3, 0xE8],
      'ｉ': [0xA3, 0xE9], 'ｊ': [0xA3, 0xEA], 'ｋ': [0xA3, 0xEB], 'ｌ': [0xA3, 0xEC],
      'ｍ': [0xA3, 0xED], 'ｎ': [0xA3, 0xEE], 'ｏ': [0xA3, 0xEF], 'ｐ': [0xA3, 0xF0],
      'ｑ': [0xA3, 0xF1], 'ｒ': [0xA3, 0xF2], 'ｓ': [0xA3, 0xF3], 'ｔ': [0xA3, 0xF4],
      'ｕ': [0xA3, 0xF5], 'ｖ': [0xA3, 0xF6], 'ｗ': [0xA3, 0xF7], 'ｘ': [0xA3, 0xF8],
      'ｙ': [0xA3, 0xF9], 'ｚ': [0xA3, 0xFA],

      // 全角标点符号
      '：': [0xA3, 0xBA], '；': [0xA3, 0xBB], '，': [0xA3, 0xAC], '。': [0xA3, 0xAE],
      '？': [0xA3, 0xBF], '！': [0xA3, 0xA1], '（': [0xA3, 0xA8], '）': [0xA3, 0xA9],
      '［': [0xA3, 0xDB], '］': [0xA3, 0xDD], '｛': [0xA3, 0xFB], '｝': [0xA3, 0xFD],
      '＂': [0xA3, 0xA2], '＇': [0xA3, 0xA7], '｀': [0xA3, 0xE0], '～': [0xA3, 0xFE],

      // 全角其他符号
      '＠': [0xA3, 0xC0], '＃': [0xA3, 0xA3], '＄': [0xA3, 0xA4], '％': [0xA3, 0xA5],
      '＾': [0xA3, 0xDE], '＆': [0xA3, 0xA6], '＊': [0xA3, 0xAA], '＿': [0xA3, 0xDF],
      '＋': [0xA3, 0xAB], '－': [0xA3, 0xAD], '＝': [0xA3, 0xBD], '｜': [0xA3, 0xDC],
      '＼': [0xA3, 0xDC], '／': [0xA3, 0xAF], '＜': [0xA3, 0xBC], '＞': [0xA3, 0xBE]
    };

    // 特殊符号映射
    this.symbolMap = {
      // 常用标点符号（中文）
      '，': [0xA1, 0xA2], '。': [0xA1, 0xA3], '；': [0xA1, 0xA4], '？': [0xA1, 0xA8],
      '！': [0xA1, 0xA9], '"': [0xA1, 0xB0], '"': [0xA1, 0xB1],
      "'": [0xA1, 0xAE], "'": [0xA1, 0xAF], '（': [0xA1, 0xB4], '）': [0xA1, 0xB5],
      '【': [0xA1, 0xBE], '】': [0xA1, 0xBF], '《': [0xA1, 0xB6], '》': [0xA1, 0xB7],
      '〈': [0xA1, 0xB8], '〉': [0xA1, 0xB9], '「': [0xA1, 0xBA], '」': [0xA1, 0xBB],
      '『': [0xA1, 0xBC], '』': [0xA1, 0xBD], '［': [0xA1, 0xC0], '］': [0xA1, 0xC1],
      '｛': [0xA1, 0xC2], '｝': [0xA1, 0xC3], '〔': [0xA1, 0xC4], '〕': [0xA1, 0xC5],

      // 数学符号
      '＋': [0xA1, 0xAB], '－': [0xA1, 0xAC], '×': [0xA1, 0xC1], '÷': [0xA1, 0xC2],
      '＝': [0xA1, 0xAD], '≠': [0xA1, 0xC7], '≤': [0xA1, 0xC8], '≥': [0xA1, 0xC9],
      '∞': [0xA1, 0xE2], '∴': [0xA1, 0xE3], '∵': [0xA1, 0xE4], '∈': [0xA1, 0xE5],
      '∩': [0xA1, 0xE6], '∪': [0xA1, 0xE7], '⊥': [0xA1, 0xE8], '∥': [0xA1, 0xE9],
      '∠': [0xA1, 0xEA], '⌒': [0xA1, 0xEB], '⊙': [0xA1, 0xEC], '√': [0xA1, 0xED],

      // 货币符号
      '￥': [0xA1, 0xE1], '＄': [0xA3, 0xA4], '￡': [0xA1, 0xE0], '€': [0xA2, 0xE3],

      // 单位符号
      '℃': [0xA1, 0xE9], '℉': [0xA2, 0xC9], '°': [0xA1, 0xE8], '′': [0xA1, 0xC6],
      '″': [0xA1, 0xC7], '‰': [0xA1, 0xEB], '％': [0xA1, 0xEA], '‱': [0xA2, 0xF0],

      // 箭头符号
      '←': [0xA1, 0xF6], '→': [0xA1, 0xF5], '↑': [0xA1, 0xF7], '↓': [0xA1, 0xF8],
      '↖': [0xA1, 0xF9], '↗': [0xA1, 0xFA], '↙': [0xA1, 0xFB], '↘': [0xA1, 0xFC],

      // 制表符号
      '┌': [0xA2, 0xA1], '┐': [0xA2, 0xA2], '└': [0xA2, 0xA3], '┘': [0xA2, 0xA4],
      '├': [0xA2, 0xA5], '┤': [0xA2, 0xA6], '┬': [0xA2, 0xA7], '┴': [0xA2, 0xA8],
      '┼': [0xA2, 0xA9], '─': [0xA2, 0xAA], '│': [0xA2, 0xAB], '■': [0xA2, 0xF1],
      '□': [0xA2, 0xF2], '▲': [0xA2, 0xF3], '△': [0xA2, 0xF4], '●': [0xA2, 0xF5],
      '○': [0xA2, 0xF6], '◆': [0xA2, 0xF7], '◇': [0xA2, 0xF8], '★': [0xA2, 0xF9],
      '☆': [0xA2, 0xFA], '※': [0xA1, 0xF7], '〓': [0xA1, 0xF8],

      // 复选框符号
      '☐': [0xA2, 0xF2], '☑': [0xA2, 0xF1], '☒': [0xA2, 0xF3], '✓': [0xA2, 0xF4],
      '✗': [0xA2, 0xF5], '✕': [0xA2, 0xF6], '✖': [0xA2, 0xF7], '✔': [0xA2, 0xF8],

      // 其他常用符号
      '…': [0xA1, 0xAD], '‖': [0xA1, 0xC0], '§': [0xA1, 0xF1], '¶': [0xA1, 0xF2],
      '†': [0xA1, 0xF3], '‡': [0xA1, 0xF4], '•': [0xA1, 0xF5], '′': [0xA1, 0xF7],
      '″': [0xA1, 0xF8], '‴': [0xA1, 0xF9], '※': [0xA1, 0xFA]
    };

    // 合并所有映射表
    this.allCharMap = {
      ...this.chineseMap,
      ...this.fullWidthMap,
      ...this.symbolMap
    };
  }

  /**
   * 将字符串编码为GBK字节数组
   * @param {string} text - 要编码的文本
   * @returns {Array} 编码后的字节数组
   */
  encode(text) {
    if (!text) return [];

    const bytes = [];

    for (let i = 0; i < text.length; i++) {
      const char = text.charAt(i);
      const code = text.charCodeAt(i);

      // 1. 优先查找预定义的字符映射
      if (this.allCharMap[char]) {
        bytes.push(...this.allCharMap[char]);
        continue;
      }

      // 2. ASCII字符直接输出
      if (code < 0x80) {
        bytes.push(code);
        continue;
      }

      // 3. 尝试通用的中文字符编码
      const gbkBytes = this.encodeChineseChar(char);
      if (gbkBytes.length > 0) {
        bytes.push(...gbkBytes);
        continue;
      }

      // 4. 最后使用UTF-8编码作为降级方案
      const utf8Bytes = this.encodeUTF8(char);
      bytes.push(...utf8Bytes);
    }

    return bytes;
  }

  /**
   * 通用中文字符编码（基于Unicode范围）
   * @param {string} char - 单个字符
   * @returns {Array} GBK字节数组
   */
  encodeChineseChar(char) {
    const code = char.charCodeAt(0);

    // 中文字符范围 (U+4E00-U+9FFF)
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 简化的GBK编码算法
      // 这里使用一个简化的映射算法
      const offset = code - 0x4E00;
      const high = Math.floor(offset / 190) + 0xB0;
      const low = (offset % 190) + 0xA1;

      // 确保在有效的GBK范围内
      if (high >= 0xB0 && high <= 0xF7 && low >= 0xA1 && low <= 0xFE) {
        return [high, low];
      }
    }

    return [];
  }

  /**
   * UTF-8编码（降级方案）
   * @param {string} char - 单个字符
   * @returns {Array} UTF-8字节数组
   */
  encodeUTF8(char) {
    const code = char.charCodeAt(0);

    if (code < 0x80) {
      // ASCII字符
      return [code];
    } else if (code < 0x800) {
      // 双字节UTF-8
      return [
        0xC0 | (code >> 6),
        0x80 | (code & 0x3F)
      ];
    } else if (code < 0x10000) {
      // 三字节UTF-8
      return [
        0xE0 | (code >> 12),
        0x80 | ((code >> 6) & 0x3F),
        0x80 | (code & 0x3F)
      ];
    } else {
      // 四字节UTF-8
      return [
        0xF0 | (code >> 18),
        0x80 | ((code >> 12) & 0x3F),
        0x80 | ((code >> 6) & 0x3F),
        0x80 | (code & 0x3F)
      ];
    }
  }

  /**
   * 检测字符串是否包含中文
   * @param {string} str - 要检测的字符串
   * @returns {boolean} 是否包含中文
   */
  hasChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
  }

  /**
   * 计算字符串的显示宽度（中文=2，英文=1）
   * @param {string} str - 要计算的字符串
   * @returns {number} 显示宽度
   */
  getDisplayWidth(str) {
    let width = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      const code = char.charCodeAt(0);

      // 中文字符、全角字符占2个宽度
      if (this.isFullWidth(char) || this.hasChinese(char)) {
        width += 2;
      } else {
        width += 1;
      }
    }
    return width;
  }

  /**
   * 按显示宽度截取字符串
   * @param {string} str - 要截取的字符串
   * @param {number} maxWidth - 最大显示宽度
   * @returns {string} 截取后的字符串
   */
  truncateByWidth(str, maxWidth) {
    let width = 0;
    let result = '';

    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      const charWidth = this.isFullWidth(char) || this.hasChinese(char) ? 2 : 1;

      if (width + charWidth > maxWidth) {
        break;
      }

      result += char;
      width += charWidth;
    }

    return result;
  }

  /**
   * 判断字符是否为全角字符
   * @param {string} char - 单个字符
   * @returns {boolean} 是否为全角字符
   */
  isFullWidth(char) {
    const code = char.charCodeAt(0);

    // 全角字符范围
    return (code >= 0xFF01 && code <= 0xFF5E) || // 全角ASCII
           (code >= 0x3000 && code <= 0x303F) || // CJK符号和标点
           (code >= 0x2E80 && code <= 0x2EFF) || // CJK部首补充
           (code >= 0x2F00 && code <= 0x2FDF) || // 康熙部首
           (code >= 0x3040 && code <= 0x309F) || // 平假名
           (code >= 0x30A0 && code <= 0x30FF) || // 片假名
           (code >= 0x3100 && code <= 0x312F) || // 注音字母
           (code >= 0x3200 && code <= 0x32FF) || // 带圈CJK字母和月份
           (code >= 0x3300 && code <= 0x33FF) || // CJK兼容
           (code >= 0x4E00 && code <= 0x9FFF) || // CJK统一汉字
           (code >= 0xF900 && code <= 0xFAFF) || // CJK兼容汉字
           (code >= 0xFE30 && code <= 0xFE4F);   // CJK兼容形式
  }
}

module.exports = ChineseTextEncoder;
