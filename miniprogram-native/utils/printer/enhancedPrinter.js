/**
 * 增强的打印工具类
 * 解决中文乱码、数据分包、格式处理等问题
 */

const ChineseTextEncoder = require('./textEncoder');
const BluetoothTransfer = require('./bluetoothTransfer');
const PrintFormatUtils = require('./formatUtils');

class EnhancedPrinter {
  constructor() {
    this.textEncoder = new ChineseTextEncoder();
    this.bluetoothTransfer = new BluetoothTransfer({
      maxChunkSize: 20,
      delay: 50,  // 增加延时确保稳定性
      retryCount: 3,
      retryDelay: 100
    });
    this.formatUtils = new PrintFormatUtils();
  }

  /**
   * 替代扩展运算符的数组合并函数
   */
  pushArray(target, source) {
    for (let i = 0; i < source.length; i++) {
      target.push(source[i]);
    }
  }

  /**
   * 增强的字符串转字节数组（支持中文）
   * @param {string} str - 字符串
   * @returns {Array} 字节数组
   */
  stringToBytes(str) {
    return this.textEncoder.encode(str);
  }

  /**
   * 增强的GBK编码处理
   * @param {string} str - 字符串
   * @returns {Array} GBK编码字节数组
   */
  stringToGBK(str) {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      const code = str.charCodeAt(i);

      if (code < 0x80) {
        // ASCII字符直接输出
        bytes.push(code);
      } else {
        // 中文字符处理
        const gbkBytes = this.chineseToGBK(char);
        if (gbkBytes.length > 0) {
          this.pushArray(bytes, gbkBytes);
        } else {
          // 如果无法转换，使用UTF-8编码
          const utf8Bytes = this.textEncoder.encode(char);
          this.pushArray(bytes, utf8Bytes);
        }
      }
    }
    return bytes;
  }

  /**
   * 常用中文字符的GBK编码映射
   * @param {string} char - 中文字符
   * @returns {Array} GBK编码字节数组
   */
  chineseToGBK(char) {
    const commonChars = {
      '样': [0xD1, 0xF9], '品': [0xC6, 0xB7], '类': [0xC0, 0xE0], '别': [0xB1, 0xF0],
      '编': [0xB1, 0xE0], '号': [0xBA, 0xC5], '采': [0xB2, 0xC9], '日': [0xC8, 0xD5],
      '期': [0xC6, 0xDA], '点': [0xB5, 0xE3], '位': [0xCE, 0xBB], '检': [0xBC, 0xEC],
      '测': [0xB2, 0xE2], '项': [0xCF, 0xEE], '目': [0xC4, 0xBF], '保': [0xB1, 0xA3],
      '存': [0xB4, 0xE6], '容': [0xC8, 0xDD], '器': [0xC6, 0xF7], '方': [0xB7, 0xBD],
      '式': [0xCA, 0xBD], '状': [0xD7, 0xB4], '态': [0xCC, 0xAC], '待': [0xB4, 0xFD],
      '合': [0xBA, 0xCF], '格': [0xB8, 0xF1], '不': [0xB2, 0xBB], '打': [0xB4, 0xF2],
      '印': [0xD3, 0xA1], '试': [0xCA, 0xD4], '设': [0xC9, 0xE8], '备': [0xB1, 0xB8],
      '时': [0xCA, 0xB1], '间': [0xBC, 0xE4], '纸': [0xD6, 0xBD], '张': [0xD5, 0xC5],
      '系': [0xCF, 0xB5], '统': [0xCD, 0xB3], '成': [0xB3, 0xC9], '功': [0xB9, 0xA6],
      '水': [0xCB, 0xAE], '土': [0xCD, 0xC1], '气': [0xC6, 0xF8], '食': [0xCA, 0xB3],
      '周': [0xD6, 0xDC], '期': [0xC6, 0xDA], '瓶': [0xC6, 0xBF], '组': [0xD7, 0xE9],
      '常': [0xB3, 0xA3], '规': [0xB9, 0xE6], '温': [0xCE, 0xC2], '塑': [0xCB, 0xDC],
      '料': [0xC1, 0xCF], '毫': [0xBA, 0xC1], '升': [0xC9, 0xFD]
    };

    if (commonChars[char]) {
      return commonChars[char];
    }

    // 如果不在常用字符中，使用默认编码
    const code = char.charCodeAt(0);
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 中文字符范围，使用简化的双字节编码
      const high = Math.floor((code - 0x4E00) / 256) + 0xA1;
      const low = ((code - 0x4E00) % 256) + 0xA1;
      return [Math.min(high, 0xFE), Math.min(low, 0xFE)];
    }

    return [];
  }

  /**
   * 增强的CPCL指令生成（支持中文和格式处理）
   * @param {Object} content - 打印内容
   * @param {Object} settings - 打印设置（可选）
   * @returns {Uint8Array} CPCL指令数组
   */
  generateEnhancedCPCLCommands(content, settings = {}) {
    const commands = [];

    // 默认设置
    const defaultSettings = {
      paperHeight: 50,
      paperWidth: 58,
      fontSize: 1,
      density: 200
    };
    const finalSettings = { ...defaultSettings, ...settings };

    // CPCL标签开始
    const labelHeight = Math.min(finalSettings.paperHeight * 6, 300);
    const labelStart = `! 0 200 200 ${labelHeight} 1\r\n`;
    this.pushArray(commands, this.stringToBytes(labelStart));

    // 添加GB2312编码声明
    this.pushArray(commands, this.stringToBytes('ENCODING GB2312\r\n'));

    // 处理内容（支持两种数据格式）
    if (content.content && Array.isArray(content.content)) {
      // 原有格式：content.content 数组
      const textItems = content.content.filter(item => item.type === 'text');
      const qrItems = content.content.filter(item => item.type === 'qrcode');

      // 处理文本内容
      textItems.forEach((item) => {
        const textX = (item.x || 2) * 4;
        const textY = (item.y || 4) * 4;
        const textCmd = `TEXT 3 0 ${textX} ${textY} `;
        this.pushArray(commands, this.stringToBytes(textCmd));

        // 使用增强的GBK编码
        const textBytes = this.stringToGBK(item.text);
        this.pushArray(commands, textBytes);
        this.pushArray(commands, this.stringToBytes('\r\n'));
      });

      // 处理二维码
      qrItems.forEach((item) => {
        const qrX = (item.x || 50) * 4;
        const qrY = (item.y || 4) * 4;
        const qrCmd = `BARCODE QR ${qrX} ${qrY} M 2 U 4\r\n`;
        this.pushArray(commands, this.stringToBytes(qrCmd));
        this.pushArray(commands, this.stringToBytes(`MA,${item.text}\r\n`));
        this.pushArray(commands, this.stringToBytes('ENDQR\r\n'));
      });
    } else {
      // 新格式：直接的样品数据对象
      let yPos = 8;
      const lineHeight = 18;  // 大幅增加行间距

      // 样品编号
      if (content.sampleId) {
        const text = `样品编号：${content.sampleId}`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 样品类型
      if (content.sampleType) {
        const text = `样品类别：${content.sampleType}`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 采样地点
      if (content.location) {
        const text = `采样地点：${content.location}`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 采样日期
      if (content.date) {
        const text = `采样日期：${content.date}`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 检测项目
      if (content.items && content.items.length > 0) {
        const text = `检测项目：${content.items.join('、')}`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 保存方式
      if (content.storage) {
        const text = `保存方式：${content.storage}`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 状态
      if (content.status) {
        const text = `状态：☐待测 ☐合格 ☐不合格`;
        const textCmd = `TEXT 3 0 8 ${yPos * 4} `;
        this.pushArray(commands, this.stringToBytes(textCmd));
        this.pushArray(commands, this.stringToGBK(text));
        this.pushArray(commands, this.stringToBytes('\r\n'));
        yPos += lineHeight;
      }

      // 添加二维码（样品编号）
      if (content.sampleId) {
        const qrCmd = `BARCODE QR 200 16 M 2 U 4\r\n`;
        this.pushArray(commands, this.stringToBytes(qrCmd));
        this.pushArray(commands, this.stringToBytes(`MA,${content.sampleId}\r\n`));
        this.pushArray(commands, this.stringToBytes('ENDQR\r\n'));
      }
    }

    // 打印标签
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'));

    return new Uint8Array(commands);
  }

  /**
   * 增强的ESC/POS指令生成（支持中文和格式处理）
   * @param {Object} device - 设备信息
   * @param {Object} settings - 打印设置
   * @param {string} content - 打印内容
   * @returns {Uint8Array} ESC/POS指令数组
   */
  generateEnhancedESCPOSCommands(device, settings, content) {
    const ESC = 0x1B;
    const GS = 0x1D;
    const commands = [];

    // 初始化打印机
    commands.push(ESC, 0x40);

    // 设置字符编码为GB2312/GBK（中文支持）
    commands.push(ESC, 0x74, 0x01);

    // 设置打印密度
    const density = settings.printDensity === 'light' ? 1 :
                   settings.printDensity === 'medium' ? 8 : 15;
    commands.push(GS, 0x7C, density);

    // 设置行间距
    commands.push(ESC, 0x33, 30);

    // 居中对齐
    commands.push(ESC, 0x61, 0x01);

    // 标题 - 加粗
    commands.push(ESC, 0x45, 0x01);
    this.pushArray(commands, this.stringToGBK('=== 样品标签 ==='));
    commands.push(0x0A, 0x0A);
    commands.push(ESC, 0x45, 0x00);

    // 左对齐
    commands.push(ESC, 0x61, 0x00);

    // 添加内容（使用格式化工具）
    if (typeof content === 'string') {
      const lines = this.formatUtils.wrapText(content, 32);
      lines.forEach(line => {
        this.pushArray(commands, this.stringToGBK(line));
        commands.push(0x0A);
      });
    }

    // 走纸
    commands.push(ESC, 0x64, 3);

    // 切纸指令（如果支持）
    commands.push(GS, 0x56, 0x00);

    return new Uint8Array(commands);
  }

  /**
   * 增强的BLE数据发送（带重试和错误处理）
   * @param {Object} device - 设备信息
   * @param {Uint8Array} data - 要发送的数据
   * @param {Function} onProgress - 进度回调
   * @returns {Promise} 发送结果
   */
  async sendEnhancedBLEData(device, data, onProgress = null) {
    console.log('开始增强发送数据，总长度:', data.length, '字节');
    
    return new Promise((resolve, reject) => {
      this.bluetoothTransfer.sendData(
        data.buffer,
        {
          deviceId: device.deviceId,
          serviceId: device.serviceId,
          characteristicId: device.characteristicId
        },
        onProgress,
        () => {
          console.log('增强数据发送完成');
          resolve();
        },
        (error) => {
          console.error('增强数据发送失败:', error);
          reject(error);
        }
      );
    });
  }

  /**
   * 生成测试打印内容（中文测试）
   * @returns {Uint8Array} 测试指令
   */
  generateChineseTestCommands() {
    const commands = [];

    // CPCL标签开始
    this.pushArray(commands, this.stringToBytes('! 0 200 200 300 1\r\n'));
    this.pushArray(commands, this.stringToBytes('ENCODING GB2312\r\n'));

    // 测试各种中文内容
    const testTexts = [
      '样品类别：水样',
      '样品编号：LIMS001（1/5）',
      '采样日期：2024-01-15',
      '采样点位：监测点A（周期1）',
      '检测项目：常规检测',
      '保存容器：500ml塑料瓶',
      '保存方式：常温保存',
      '样品状态：☐待测 ☐合格 ☐不合格'
    ];

    let yPos = 10;
    testTexts.forEach((text, index) => {
      this.pushArray(commands, this.stringToBytes(`TEXT 3 0 10 ${yPos} `));
      this.pushArray(commands, this.stringToGBK(text));
      this.pushArray(commands, this.stringToBytes('\r\n'));
      yPos += 20;
    });

    // 添加二维码
    this.pushArray(commands, this.stringToBytes('BARCODE QR 200 10 M 2 U 4\r\n'));
    this.pushArray(commands, this.stringToBytes('MA,LIMS001-TEST\r\n'));
    this.pushArray(commands, this.stringToBytes('ENDQR\r\n'));

    // 打印标签
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'));

    return new Uint8Array(commands);
  }

  /**
   * 获取打印状态
   * @returns {Object} 打印状态信息
   */
  getStatus() {
    return {
      transferStatus: this.bluetoothTransfer.getTransferStatus(),
      formatConfig: this.formatUtils.getConfig()
    };
  }
}

module.exports = EnhancedPrinter;
