// 基于标准text-encoding库的微信小程序适配版本
// 支持UTF-8、GBK、GB2312等多种编码格式

(function(global) {
  'use strict';

  // 工具函数
  function inRange(a, min, max) {
    return min <= a && a <= max;
  }

  function includes(array, item) {
    return array.indexOf(item) !== -1;
  }

  var floor = Math.floor;

  function ToDictionary(o) {
    if (o === undefined) return {};
    if (o === Object(o)) return o;
    throw TypeError('Could not convert argument to dictionary');
  }

  // 字符串转码点数组
  function stringToCodePoints(string) {
    var s = String(string);
    var n = s.length;
    var i = 0;
    var u = [];
    
    while (i < n) {
      var c = s.charCodeAt(i);
      if (c < 0xD800 || c > 0xDFFF) {
        u.push(c);
      } else if (0xDC00 <= c && c <= 0xDFFF) {
        u.push(0xFFFD);
      } else if (0xD800 <= c && c <= 0xDBFF) {
        if (i === n - 1) {
          u.push(0xFFFD);
        } else {
          var d = s.charCodeAt(i + 1);
          if (0xDC00 <= d && d <= 0xDFFF) {
            var a = c & 0x3FF;
            var b = d & 0x3FF;
            u.push(0x10000 + (a << 10) + b);
            i += 1;
          } else {
            u.push(0xFFFD);
          }
        }
      }
      i += 1;
    }
    return u;
  }

  // 编码器和解码器常量
  var end_of_stream = -1;
  var finished = -1;

  // Stream类
  function Stream(tokens) {
    this.tokens = [].slice.call(tokens);
    this.index = 0;
  }

  Stream.prototype.read = function() {
    if (this.index >= this.tokens.length) {
      return end_of_stream;
    }
    return this.tokens[this.index++];
  };

  // UTF-8编码器
  function UTF8Encoder(options) {
    var fatal = options.fatal;
    this.handler = function(stream, code_point) {
      if (code_point === end_of_stream)
        return finished;
      
      if (inRange(code_point, 0x0000, 0x007F))
        return code_point;
      
      var count, offset;
      if (inRange(code_point, 0x0080, 0x07FF)) {
        count = 1;
        offset = 0xC0;
      } else if (inRange(code_point, 0x0800, 0xFFFF)) {
        count = 2;
        offset = 0xE0;
      } else if (inRange(code_point, 0x10000, 0x10FFFF)) {
        count = 3;
        offset = 0xF0;
      }
      
      var bytes = [(code_point >> (6 * count)) + offset];
      while (count > 0) {
        var temp = code_point >> (6 * (count - 1));
        bytes.push(0x80 | (temp & 0x3F));
        count -= 1;
      }
      return bytes;
    };
  }

  // UTF-8解码器
  function UTF8Decoder(options) {
    var fatal = options.fatal;
    var utf8_code_point = 0;
    var utf8_bytes_seen = 0;
    var utf8_bytes_needed = 0;
    var utf8_lower_boundary = 0x80;
    var utf8_upper_boundary = 0xBF;
    
    this.handler = function(stream, bite) {
      if (bite === end_of_stream) {
        if (utf8_bytes_needed !== 0) {
          utf8_bytes_needed = 0;
          return 0xFFFD;
        }
        return finished;
      }
      
      if (utf8_bytes_needed === 0) {
        if (inRange(bite, 0x00, 0x7F)) {
          return bite;
        } else if (inRange(bite, 0xC2, 0xDF)) {
          utf8_bytes_needed = 1;
          utf8_code_point = bite & 0x1F;
        } else if (inRange(bite, 0xE0, 0xEF)) {
          if (bite === 0xE0) utf8_lower_boundary = 0xA0;
          if (bite === 0xED) utf8_upper_boundary = 0x9F;
          utf8_bytes_needed = 2;
          utf8_code_point = bite & 0xF;
        } else if (inRange(bite, 0xF0, 0xF4)) {
          if (bite === 0xF0) utf8_lower_boundary = 0x90;
          if (bite === 0xF4) utf8_upper_boundary = 0x8F;
          utf8_bytes_needed = 3;
          utf8_code_point = bite & 0x7;
        } else {
          return 0xFFFD;
        }
        return null;
      }
      
      if (!inRange(bite, utf8_lower_boundary, utf8_upper_boundary)) {
        utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;
        utf8_lower_boundary = 0x80;
        utf8_upper_boundary = 0xBF;
        stream.index--;
        return 0xFFFD;
      }
      
      utf8_lower_boundary = 0x80;
      utf8_upper_boundary = 0xBF;
      utf8_code_point = (utf8_code_point << 6) | (bite & 0x3F);
      utf8_bytes_seen += 1;
      
      if (utf8_bytes_seen !== utf8_bytes_needed) {
        return null;
      }
      
      var code_point = utf8_code_point;
      utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;
      return code_point;
    };
  }

  // 常用中文字符GBK编码映射
  var chineseGBKMap = {
    '测': [0xB2, 0xE2], '试': [0xCA, 0xD4], '打': [0xB4, 0xF2], '印': [0xD3, 0xA1],
    '样': [0xD1, 0xF9], '品': [0xC6, 0xB7], '类': [0xC0, 0xE0], '别': [0xB1, 0xF0],
    '编': [0xB1, 0xE0], '号': [0xBA, 0xC5], '采': [0xB2, 0xC9], '日': [0xC8, 0xD5],
    '期': [0xC6, 0xDA], '时': [0xCA, 0xB1], '间': [0xBC, 0xE4], '点': [0xB5, 0xE3],
    '位': [0xCE, 0xBB], '检': [0xBC, 0xEC], '项': [0xCF, 0xEE], '目': [0xC4, 0xBF],
    '保': [0xB1, 0xA3], '存': [0xB4, 0xE6], '容': [0xC8, 0xDD], '器': [0xC6, 0xF7],
    '方': [0xB7, 0xBD], '式': [0xCA, 0xBD], '状': [0xD7, 0xB4], '态': [0xCC, 0xAC],
    '系': [0xCF, 0xB5], '统': [0xCD, 0xB3], '成': [0xB3, 0xC9], '功': [0xB9, 0xA6],
    '水': [0xCB, 0xAE], '温': [0xCE, 0xC2], '度': [0xB6, 0xC8], '值': [0xD6, 0xB5],
    '量': [0xC1, 0xBF], '质': [0xD6, 0xCA], '浓': [0xC5, 0xA8], '密': [0xC3, 0xDC],
    '公': [0xB9, 0xAB], '司': [0xCB, 0xBE], '环': [0xBB, 0xB7], '境': [0xBE, 0xB3],
    '监': [0xBC, 0xE0], '有': [0xD3, 0xD0], '限': [0xCF, 0xDE], '浙': [0xD5, 0xE3],
    '江': [0xBD, 0xAD], '求': [0xC7, 0xF3], '实': [0xCA, 0xB5], '地': [0xB5, 0xD8],
    '下': [0xCF, 0xC2], '第': [0xB5, 0xDA], '二': [0xB6, 0xFE], '周': [0xD6, 0xDC]
  };

  // GBK编码器（简化版）
  function GBKEncoder(options) {
    var fatal = options.fatal;
    this.handler = function(stream, code_point) {
      if (code_point === end_of_stream)
        return finished;
      
      if (inRange(code_point, 0x0000, 0x007F))
        return code_point;
      
      var char = String.fromCharCode(code_point);
      if (chineseGBKMap[char]) {
        return chineseGBKMap[char];
      }
      
      return 0x3F; // '?' 替代字符
    };
  }

  // GBK解码器（简化版）
  function GBKDecoder(options) {
    var fatal = options.fatal;
    var gbk_first = 0x00;

    this.handler = function(stream, bite) {
      if (bite === end_of_stream) {
        if (gbk_first !== 0x00) {
          gbk_first = 0x00;
          return fatal ? null : 0xFFFD;
        }
        return finished;
      }

      if (gbk_first === 0x00) {
        if (inRange(bite, 0x00, 0x7F)) {
          return bite;
        }
        if (inRange(bite, 0x81, 0xFE)) {
          gbk_first = bite;
          return null;
        }
        return fatal ? null : 0xFFFD;
      }

      var lead = gbk_first;
      gbk_first = 0x00;

      if (inRange(bite, 0x40, 0xFE) && bite !== 0x7F) {
        // 查找对应的中文字符
        for (var char in chineseGBKMap) {
          var gbkBytes = chineseGBKMap[char];
          if (gbkBytes[0] === lead && gbkBytes[1] === bite) {
            return char.charCodeAt(0);
          }
        }
      }

      // 如果找不到对应字符，返回替代字符
      stream.index--; // 回退一个字节
      return fatal ? null : 0xFFFD;
    };
  }

  // 编码器映射
  var encoders = {};
  encoders['utf-8'] = function(options) {
    return new UTF8Encoder(options);
  };
  encoders['gbk'] = function(options) {
    return new GBKEncoder(options);
  };
  encoders['gb2312'] = function(options) {
    return new GBKEncoder(options); // 使用GBK编码器
  };

  // 解码器映射
  var decoders = {};
  decoders['utf-8'] = function(options) {
    return new UTF8Decoder(options);
  };
  decoders['gbk'] = function(options) {
    return new GBKDecoder(options);
  };
  decoders['gb2312'] = function(options) {
    return new GBKDecoder(options); // 使用GBK解码器
  };

  // 获取编码信息
  function getEncoding(label) {
    var encoding = label.toLowerCase();
    switch (encoding) {
      case 'utf-8':
      case 'utf8':
        return { name: 'utf-8' };
      case 'gbk':
      case 'gb2312':
        return { name: encoding };
      default:
        return null;
    }
  }

  // TextEncoder类
  function TextEncoder(label, options) {
    if (!(this instanceof TextEncoder))
      throw TypeError('Called as a function. Did you forget \'new\'?');
    
    options = ToDictionary(options);
    
    this._encoding = null;
    this._encoder = null;
    this._do_not_flush = false;
    this._fatal = Boolean(options['fatal']) ? 'fatal' : 'replacement';
    
    var enc = this;
    
    if (Boolean(options['NONSTANDARD_allowLegacyEncoding'])) {
      label = label !== undefined ? String(label) : 'utf-8';
      var encoding = getEncoding(label);
      if (encoding === null)
        throw RangeError('Unknown encoding: ' + label);
      if (!encoders[encoding.name]) {
        throw Error('Encoder not present for encoding: ' + encoding.name);
      }
      enc._encoding = encoding;
    } else {
      enc._encoding = getEncoding('utf-8');
      if (label !== undefined && typeof console !== 'undefined') {
        console.warn('TextEncoder constructor called with encoding label, which is ignored.');
      }
    }
    
    return enc;
  }

  if (typeof Object.defineProperty === 'function') {
    Object.defineProperty(TextEncoder.prototype, 'encoding', {
      get: function() { return this._encoding.name.toLowerCase(); }
    });
  } else {
    TextEncoder.prototype.encoding = 'utf-8';
  }

  TextEncoder.prototype.encode = function encode(opt_string, options) {
    opt_string = opt_string === undefined ? '' : String(opt_string);
    options = ToDictionary(options);
    
    if (!this._do_not_flush)
      this._encoder = encoders[this._encoding.name]({
        fatal: this._fatal === 'fatal'});
    this._do_not_flush = Boolean(options['stream']);
    
    var input = new Stream(stringToCodePoints(opt_string));
    var output = [];
    var result;
    
    while (true) {
      var token = input.read();
      if (token === end_of_stream)
        break;
      result = this._encoder.handler(input, token);
      if (result === finished)
        break;
      if (Array.isArray(result))
        output.push.apply(output, result);
      else
        output.push(result);
    }
    
    if (!this._do_not_flush) {
      while (true) {
        result = this._encoder.handler(input, input.read());
        if (result === finished)
          break;
        if (Array.isArray(result))
          output.push.apply(output, result);
        else
          output.push(result);
      }
      this._encoder = null;
    }
    
    return new Uint8Array(output);
  };

  // TextDecoder类
  function TextDecoder(label, options) {
    if (!(this instanceof TextDecoder))
      throw TypeError('Called as a function. Did you forget \'new\'?');

    label = label !== undefined ? String(label) : 'utf-8';
    options = ToDictionary(options);

    this._encoding = null;
    this._decoder = null;
    this._ignoreBOM = false;
    this._BOMseen = false;
    this._error_mode = 'replacement';
    this._do_not_flush = false;

    var encoding = getEncoding(label);
    if (encoding === null)
      throw RangeError('Unknown encoding: ' + label);
    if (!decoders[encoding.name]) {
      throw Error('Decoder not present for encoding: ' + encoding.name);
    }

    var dec = this;
    dec._encoding = encoding;

    if (Boolean(options['fatal']))
      dec._error_mode = 'fatal';

    if (Boolean(options['ignoreBOM']))
      dec._ignoreBOM = true;

    return dec;
  }

  if (typeof Object.defineProperty === 'function') {
    Object.defineProperty(TextDecoder.prototype, 'encoding', {
      get: function() { return this._encoding.name.toLowerCase(); }
    });

    Object.defineProperty(TextDecoder.prototype, 'fatal', {
      get: function() { return this._error_mode === 'fatal'; }
    });

    Object.defineProperty(TextDecoder.prototype, 'ignoreBOM', {
      get: function() { return this._ignoreBOM; }
    });
  } else {
    TextDecoder.prototype.encoding = 'utf-8';
    TextDecoder.prototype.fatal = false;
    TextDecoder.prototype.ignoreBOM = false;
  }

  TextDecoder.prototype.decode = function decode(input, options) {
    var bytes;
    if (typeof input === 'object' && input instanceof ArrayBuffer) {
      bytes = new Uint8Array(input);
    } else if (typeof input === 'object' &&
               ('buffer' in input && input.buffer instanceof ArrayBuffer)) {
      bytes = new Uint8Array(input.buffer, input.byteOffset, input.byteLength);
    } else {
      bytes = new Uint8Array(0);
    }

    options = ToDictionary(options);

    if (!this._do_not_flush) {
      this._decoder = decoders[this._encoding.name]({
        fatal: this._error_mode === 'fatal'});
      this._BOMseen = false;
    }
    this._do_not_flush = Boolean(options['stream']);

    var input_stream = new Stream(bytes);
    var output = [];
    var result;

    while (true) {
      var token = input_stream.read();
      if (token === end_of_stream)
        break;
      result = this._decoder.handler(input_stream, token);
      if (result === finished)
        break;
      if (result !== null) {
        if (Array.isArray(result))
          output.push.apply(output, result);
        else
          output.push(result);
      }
    }

    if (!this._do_not_flush) {
      do {
        result = this._decoder.handler(input_stream, input_stream.read());
        if (result === finished)
          break;
        if (result !== null) {
          if (Array.isArray(result))
            output.push.apply(output, result);
          else
            output.push(result);
        }
      } while (!this._do_not_flush);
      this._decoder = null;
    }

    // 将码点转换为字符串
    var string = '';
    for (var i = 0; i < output.length; i++) {
      var code_point = output[i];
      if (code_point <= 0xFFFF) {
        string += String.fromCharCode(code_point);
      } else {
        code_point -= 0x10000;
        string += String.fromCharCode(0xD800 + ((code_point >> 10) & 0x3FF));
        string += String.fromCharCode(0xDC00 + (code_point & 0x3FF));
      }
    }

    return string;
  };

  // 导出到全局
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TextEncoder: TextEncoder, TextDecoder: TextDecoder };
  } else {
    global.TextEncoder = TextEncoder;
    global.TextDecoder = TextDecoder;
  }

})(typeof global !== 'undefined' ? global : this);
