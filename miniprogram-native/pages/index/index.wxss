/* pages/index/index.wxss */

/* 头部欢迎区域 */
.header-section {
  margin-bottom: 30rpx;
}

.welcome-card {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  opacity: 0.8;
}



/* 快捷功能区域 */
.quick-actions {
  margin-bottom: 30rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
}

.more-text {
  font-size: 24rpx;
  color: #409EFF;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  padding: 0 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 35rpx 15rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon image {
  width: 40rpx;
  height: 40rpx;
}

.scan-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
}

.task-icon {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
}



.sample-icon {
  background: linear-gradient(135deg, #F56C6C, #E6A23C);
}

.printer-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.action-text {
  font-size: 24rpx;
  color: #303133;
  text-align: center;
}

/* 最近任务 */
.recent-tasks {
  margin-bottom: 30rpx;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.task-item {
  padding: 30rpx;
  transition: transform 0.2s;
}

.task-item:active {
  transform: scale(0.98);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.task-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  flex: 1;
  margin-right: 20rpx;
}

.task-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.status-pending {
  background: #f0f9ff;
  color: #409EFF;
}

.status-in_progress {
  background: #f0f9f0;
  color: #67C23A;
}

.status-completed {
  background: #fdf6ec;
  color: #E6A23C;
}

.status-cancelled {
  background: #fef0f0;
  color: #F56C6C;
}

.task-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.task-location,
.task-time {
  font-size: 24rpx;
  color: #909399;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #606266;
  white-space: nowrap;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 4rpx;
  transition: width 0.3s;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  background: white;
  border-radius: 20rpx;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #909399;
}

/* 系统通知 */
.notifications {
  margin-bottom: 30rpx;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.notification-item {
  padding: 25rpx 30rpx;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.notification-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #303133;
  flex: 1;
  margin-right: 20rpx;
}

.notification-time {
  font-size: 22rpx;
  color: #C0C4CC;
}

.notification-content {
  font-size: 24rpx;
  color: #606266;
  line-height: 1.6;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #606266;
}
