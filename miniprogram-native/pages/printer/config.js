// pages/printer/config.js
const app = getApp()
const CPCLPrintAdapter = require('../../utils/cpcl_print/adapter.js')

Page({
  // 辅助函数：替代扩展运算符
  pushArray: function(target, source) {
    for (let i = 0; i < source.length; i++) {
      target.push(source[i])
    }
  },

  data: {
    isScanning: false,
    allDevices: [], // 显示所有发现的设备
    connectedDevice: null,
    printerSettings: {
      paperWidth: 75,
      paperHeight: 60,
      printDensity: 'medium',
      printSpeed: 'normal',
      commandSet: 'CPCL' // 默认使用CPCL指令集
    }
  },

  onLoad() {
    console.log('打印机配置页面加载')
    this.loadSavedSettings()
    // 初始化CPCL打印适配器
    this.cpclPrinter = new CPCLPrintAdapter()
  },

  onShow() {
    this.checkBluetoothStatus()
  },

  // 加载已保存的设置
  loadSavedSettings() {
    try {
      const savedDevice = wx.getStorageSync('connectedDevice')
      const savedSettings = wx.getStorageSync('printerSettings')
      
      if (savedDevice) {
        this.setData({ connectedDevice: savedDevice })
      }
      
      if (savedSettings) {
        this.setData({ printerSettings: savedSettings })
      }
    } catch (error) {
      console.error('加载打印机设置失败:', error)
    }
  },

  // 检查蓝牙状态
  async checkBluetoothStatus() {
    try {
      const bluetoothState = await wx.getBluetoothAdapterState()
      console.log('蓝牙状态:', bluetoothState)
      
      if (!bluetoothState.available) {
        wx.showModal({
          title: '蓝牙不可用',
          content: '请检查设备是否支持蓝牙功能',
          showCancel: false
        })
      }
    } catch (error) {
      console.log('蓝牙适配器未初始化')
    }
  },

  // 搜索蓝牙设备
  async searchBluetoothDevices() {
    if (this.data.isScanning) {
      return
    }

    try {
      this.setData({ isScanning: true, bluetoothDevices: [] })

      // 使用CPCL打印适配器初始化蓝牙
      await this.cpclPrinter.initBluetooth()

      // 搜索打印机设备
      const devices = await this.cpclPrinter.searchDevices()

      this.setData({
        bluetoothDevices: devices,
        allDevices: devices,
        isScanning: false
      })

      console.log('发现打印机设备:', devices)


    } catch (error) {
      this.setData({ isScanning: false })
      console.error('搜索蓝牙设备失败:', error)

      wx.showModal({
        title: '搜索失败',
        content: error.message || '搜索蓝牙设备失败，请重试',
        showCancel: false
      })
    }
  },

  // 停止蓝牙搜索
  async stopBluetoothSearch() {
    try {
      await wx.stopBluetoothDevicesDiscovery()
      wx.hideLoading()
      this.setData({ isScanning: false })
      
      if (this.data.bluetoothDevices.length === 0) {
        wx.showToast({
          title: '未发现打印机',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('停止蓝牙搜索失败:', error)
      wx.hideLoading()
      this.setData({ isScanning: false })
    }
  },

  // 连接打印机
  async connectPrinter(e) {
    const device = e.currentTarget.dataset.device

    try {
      // 使用CPCL打印适配器连接设备
      const connectedDevice = await this.cpclPrinter.connectDevice(device.deviceId)

      // 保存连接信息
      const deviceInfo = Object.assign({}, device, connectedDevice, {
        connectedAt: new Date().toISOString()
      })

      this.setData({ connectedDevice: deviceInfo })
      wx.setStorageSync('connectedDevice', deviceInfo)

      // 设置CPCL打印适配器的连接设备信息
      this.cpclPrinter.setConnectedDevice(deviceInfo)

      wx.showToast({
        title: '连接成功',
        icon: 'success'
      })

      console.log('打印机连接成功:', deviceInfo)

    } catch (error) {
      console.error('连接打印机失败:', error)
      wx.showModal({
        title: '连接失败',
        content: error.message || '连接失败，请重试',
        showCancel: false
      })
    }
  },

  // 断开打印机连接
  async disconnectPrinter() {
    if (!this.data.connectedDevice) {
      return
    }

    try {
      await wx.closeBLEConnection({
        deviceId: this.data.connectedDevice.deviceId
      })
      
      this.setData({ connectedDevice: null })
      wx.removeStorageSync('connectedDevice')
      
      wx.showToast({
        title: '已断开连接',
        icon: 'success'
      })
    } catch (error) {
      console.error('断开连接失败:', error)
      wx.showToast({
        title: '断开失败',
        icon: 'error'
      })
    }
  },

  // 测试打印
  async testPrint() {
    if (!this.data.connectedDevice) {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      return
    }

    // 显示测试选项
    wx.showActionSheet({
      itemList: ['CPCL标准测试', 'CPCL中文测试', 'CPCL格式测试', 'CPCL二维码测试'],
      success: async (res) => {
        try {
          wx.showLoading({ title: '测试打印中...' })
          console.log('开始CPCL测试打印，类型:', res.tapIndex)

          let testSampleData

          if (res.tapIndex === 0) {
            // CPCL标准测试
            testSampleData = {
              sampleId: 'CPCL-TEST-001',
              sampleType: 'Standard Test',
              location: 'Test Location',
              date: '2024-01-15',
              items: ['Basic Test'],
              storage: 'Normal',
              status: 'Testing'
            }
          } else if (res.tapIndex === 1) {
            // CPCL中文测试
            testSampleData = {
              sampleId: 'LIMS-中文-001',
              sampleType: '水样（地下水）',
              location: '采样点A区域',
              date: '2024年01月15日',
              items: ['常规检测', '重金属分析', '微生物检测'],
              storage: '冷藏保存（4℃）',
              status: '待测'
            }
          } else if (res.tapIndex === 2) {
            // CPCL格式测试
            testSampleData = {
              sampleId: 'FORMAT-TEST-001',
              sampleType: '格式对齐测试样品',
              location: '这是一个很长的地点名称用来测试文本换行和格式处理功能',
              date: '2024-01-15 14:30:25',
              items: ['项目一', '项目二', '项目三', '项目四', '项目五'],
              storage: '特殊保存条件测试',
              status: '格式测试中'
            }
          } else {
            // CPCL二维码测试
            testSampleData = {
              sampleId: 'QR-CODE-TEST-001',
              sampleType: '二维码测试',
              location: '二维码生成测试',
              date: '2024-01-15',
              items: ['二维码功能测试'],
              storage: '测试存储',
              status: '二维码测试'
            }
          }

          // 使用CPCL打印适配器进行打印
          const result = await this.cpclPrinter.printSample(testSampleData, (progress) => {
            wx.showLoading({
              title: `打印中... ${progress.percentage}%`
            })
          })

          wx.hideLoading()

          if (result.success) {
            wx.showToast({
              title: '测试打印完成',
              icon: 'success'
            })
          } else {
            throw new Error(result.message)
          }

        } catch (error) {
          wx.hideLoading()
          console.error('测试打印失败:', error)
          wx.showModal({
            title: '打印失败',
            content: error.message || '测试打印失败，请重试',
            showCancel: false
          })
        }
      }
    })
  },

  // 生成基础测试指令
  generateBasicTestCommands() {
    console.log('生成基础CPCL测试指令')

    const commands = []

    // 最简单的CPCL指令
    this.pushArray(commands, this.stringToBytes('! 0 200 200 200 1\r\n'))
    this.pushArray(commands, this.stringToBytes('TEXT 1 0 10 10 Basic Test\r\n'))
    this.pushArray(commands, this.stringToBytes('TEXT 1 0 10 30 CPCL Printer\r\n'))
    this.pushArray(commands, this.stringToBytes('TEXT 1 0 10 50 Font Size Test\r\n'))
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成测试打印指令
  generateTestPrintCommands() {
    const device = this.data.connectedDevice
    const settings = this.data.printerSettings

    // 根据设置选择指令集
    if (settings.commandSet === 'CPCL') {
      return this.generateCPCLCommands(device, settings)
    } else {
      return this.generateESCPOSCommands(device, settings)
    }
  },

  // 生成ESC/POS指令
  generateESCPOSCommands(device, settings) {
    console.log('生成ESC/POS指令')

    const ESC = 0x1B
    const GS = 0x1D
    const commands = []

    // 初始化打印机
    commands.push(ESC, 0x40)

    // 设置字符编码为GB2312/GBK（中文支持）
    commands.push(ESC, 0x74, 0x01)

    // 设置打印密度和速度
    const density = settings.printDensity === 'light' ? 1 :
                   settings.printDensity === 'medium' ? 8 : 15
    commands.push(GS, 0x7C, density)

    // 设置行间距
    commands.push(ESC, 0x33, 30)

    // 居中对齐
    commands.push(ESC, 0x61, 0x01)

    // 标题 - 加粗
    commands.push(ESC, 0x45, 0x01)
    this.pushArray(commands, this.stringToGBK('=== 打印测试 ==='))
    commands.push(0x0A, 0x0A)
    commands.push(ESC, 0x45, 0x00)

    // 左对齐
    commands.push(ESC, 0x61, 0x00)

    // 设备信息
    const deviceName = `设备: ${device.displayName || device.name || '未知设备'}`
    this.pushArray(commands, this.stringToGBK(deviceName))
    commands.push(0x0A)

    // 时间信息
    const timeStr = `时间: ${new Date().toLocaleString()}`
    this.pushArray(commands, this.stringToGBK(timeStr))
    commands.push(0x0A)

    // 设置信息
    const sizeStr = `纸张: ${settings.paperWidth}x${settings.paperHeight}mm`
    this.pushArray(commands, this.stringToGBK(sizeStr))
    commands.push(0x0A)

    // 分割线
    this.pushArray(commands, this.stringToGBK('------------------------'))
    commands.push(0x0A)

    // 测试文本
    this.pushArray(commands, this.stringToGBK('LIMS系统打印测试成功!'))
    commands.push(0x0A, 0x0A, 0x0A)

    // 走纸
    commands.push(ESC, 0x64, 3) // 走纸3行

    // 切纸指令（如果支持）
    commands.push(GS, 0x56, 0x00)

    return new Uint8Array(commands)
  },

  // 生成CPCL指令
  generateCPCLCommands(device, settings) {
    console.log('生成CPCL指令')

    const commands = []

    // CPCL标签开始 - 使用较小的DPI和尺寸
    const labelHeight = Math.min(settings.paperHeight * 6, 300) // 限制高度
    const labelStart = `! 0 200 200 ${labelHeight} 1\r\n`
    this.pushArray(commands, this.stringToBytes(labelStart))

    // 使用纯ASCII内容进行测试
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 10 === Print Test ===\r\n'))

    // 设备信息 - 纯英文
    const deviceName = device.displayName || device.name || 'Unknown Device'
    this.pushArray(commands, this.stringToBytes(`TEXT 3 0 10 30 Device: ${deviceName}\r\n`))

    // 时间信息 - 纯英文
    const now = new Date()
    const timeStr = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')} ${now.getHours().toString().padStart(2,'0')}:${now.getMinutes().toString().padStart(2,'0')}`
    this.pushArray(commands, this.stringToBytes(`TEXT 3 0 10 50 Time: ${timeStr}\r\n`))

    // 纸张信息 - 纯英文
    this.pushArray(commands, this.stringToBytes(`TEXT 3 0 10 70 Paper: ${settings.paperWidth}x${settings.paperHeight}mm\r\n`))

    // 分割线
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 90 ------------------------\r\n'))

    // 测试文本 - 纯英文
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 110 LIMS Print Test OK!\r\n'))

    // 添加GB2312编码声明（测试证明有效）
    this.pushArray(commands, this.stringToBytes('ENCODING GB2312\r\n'))

    // 中文测试 - 使用GBK编码
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 130 '))
    const gbkEncoder = new TextEncoder('gbk', { NONSTANDARD_allowLegacyEncoding: true })
    const chineseBytes = Array.from(gbkEncoder.encode('LIMS系统打印测试成功！'))
    this.pushArray(commands, chineseBytes)
    this.pushArray(commands, this.stringToBytes('\r\n'))

    // 打印标签
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成中文编码测试指令
  generateChineseTestCommands() {
    console.log('生成中文编码测试指令')

    const commands = []

    // CPCL标签开始
    this.pushArray(commands, this.stringToBytes('! 0 200 200 300 1\r\n'))

    // 测试1: 使用TextEncoder UTF-8编码
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 10 UTF8: '))
    const utf8Encoder = new TextEncoder('utf-8')
    const utf8Bytes = Array.from(utf8Encoder.encode('测试'))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\r\n'))

    // 测试2: 使用TextEncoder GBK编码
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 40 GBK: '))
    const gbkEncoder = new TextEncoder('gbk', { NONSTANDARD_allowLegacyEncoding: true })
    const gbkBytes = Array.from(gbkEncoder.encode('测试'))
    this.pushArray(commands, gbkBytes)
    this.pushArray(commands, this.stringToBytes('\r\n'))

    // 测试3: 使用GB2312编码声明
    this.pushArray(commands, this.stringToBytes('ENCODING GB2312\r\n'))
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 70 GB2312: '))
    this.pushArray(commands, gbkBytes)
    this.pushArray(commands, this.stringToBytes('\r\n'))

    // 测试4: 纯ASCII
    this.pushArray(commands, this.stringToBytes('TEXT 3 0 10 100 ASCII: Test OK\r\n'))

    // 测试5: 十六进制显示
    this.pushArray(commands, this.stringToBytes('TEXT 1 0 10 130 UTF8-HEX: '))
    utf8Bytes.forEach(byte => {
      this.pushArray(commands, this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    this.pushArray(commands, this.stringToBytes('\r\n'))

    this.pushArray(commands, this.stringToBytes('TEXT 1 0 10 160 GBK-HEX: '))
    gbkBytes.forEach(byte => {
      this.pushArray(commands, this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    this.pushArray(commands, this.stringToBytes('\r\n'))

    // 打印标签
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成字体测试指令
  generateFontTestCommands() {
    console.log('生成字体测试指令')

    const commands = []

    // CPCL标签开始
    this.pushArray(commands, this.stringToBytes('! 0 200 200 400 1\r\n'))

    // 测试不同字体ID (0-7)
    const testText = 'Font Test'
    const chineseBytes = [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95] // "测试"

    let yPos = 10
    for (let fontId = 0; fontId <= 7; fontId++) {
      // 英文测试
      this.pushArray(commands, this.stringToBytes(`TEXT ${fontId} 0 10 ${yPos} Font${fontId}: ${testText}\r\n`))

      // 中文测试
      this.pushArray(commands, this.stringToBytes(`TEXT ${fontId} 0 200 ${yPos} `))
      this.pushArray(commands, chineseBytes)
      this.pushArray(commands, this.stringToBytes('\r\n'))

      yPos += 35
    }

    // 测试不同字体方向 (0=正常, 1=90度, 2=180度, 3=270度)
    yPos += 20
    this.pushArray(commands, this.stringToBytes(`TEXT 1 0 10 ${yPos} Rotation Test:\r\n`))
    yPos += 25

    for (let rotation = 0; rotation <= 3; rotation++) {
      this.pushArray(commands, this.stringToBytes(`TEXT 1 ${rotation} 10 ${yPos} R${rotation}: `))
      this.pushArray(commands, chineseBytes)
      this.pushArray(commands, this.stringToBytes('\r\n'))
      yPos += 25
    }

    // 打印标签
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成ESC/POS测试指令
  generateESCPOSTestCommands() {
    console.log('生成ESC/POS测试指令')

    const commands = []

    // ESC/POS初始化
    commands.push(0x1B, 0x40) // ESC @ - 初始化打印机

    // 设置字符集为GB2312
    commands.push(0x1C, 0x43, 0x01) // FS C 1 - 选择GB2312字符集

    // 标题 - 居中对齐
    commands.push(0x1B, 0x61, 0x01) // ESC a 1 - 居中对齐
    this.pushArray(commands, this.stringToBytes('=== ESC/POS Test ===\n'))

    // 左对齐
    commands.push(0x1B, 0x61, 0x00) // ESC a 0 - 左对齐

    // 设备信息
    const deviceName = this.data.connectedDevice.displayName || this.data.connectedDevice.name || 'Unknown Device'
    this.pushArray(commands, this.stringToBytes(`Device: ${deviceName}\n`))

    // 时间信息
    const now = new Date()
    const timeStr = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')} ${now.getHours().toString().padStart(2,'0')}:${now.getMinutes().toString().padStart(2,'0')}`
    this.pushArray(commands, this.stringToBytes(`Time: ${timeStr}\n`))

    // 分割线
    this.pushArray(commands, this.stringToBytes('------------------------\n'))

    // 字体大小测试
    this.pushArray(commands, this.stringToBytes('Font Size Test:\n'))

    // 正常字体
    this.pushArray(commands, this.stringToBytes('Normal: LIMS Print Test\n'))

    // 双倍宽度
    commands.push(0x1B, 0x21, 0x20) // ESC ! 32 - 双倍宽度
    this.pushArray(commands, this.stringToBytes('Wide: LIMS Test\n'))

    // 双倍高度
    commands.push(0x1B, 0x21, 0x10) // ESC ! 16 - 双倍高度
    this.pushArray(commands, this.stringToBytes('Tall: LIMS Test\n'))

    // 双倍宽高
    commands.push(0x1B, 0x21, 0x30) // ESC ! 48 - 双倍宽高
    this.pushArray(commands, this.stringToBytes('Big: LIMS\n'))

    // 恢复正常
    commands.push(0x1B, 0x21, 0x00) // ESC ! 0 - 正常字体

    // 加粗测试
    commands.push(0x1B, 0x45, 0x01) // ESC E 1 - 加粗开
    this.pushArray(commands, this.stringToBytes('Bold: LIMS Print Test\n'))
    commands.push(0x1B, 0x45, 0x00) // ESC E 0 - 加粗关

    // 下划线测试
    commands.push(0x1B, 0x2D, 0x01) // ESC - 1 - 下划线开
    this.pushArray(commands, this.stringToBytes('Underline: LIMS Test\n'))
    commands.push(0x1B, 0x2D, 0x00) // ESC - 0 - 下划线关

    // 走纸
    this.pushArray(commands, this.stringToBytes('\n\n\n'))

    // 切纸（如果支持）
    commands.push(0x1D, 0x56, 0x00) // GS V 0 - 全切

    return new Uint8Array(commands)
  },

  // 生成ESC/POS中文测试指令
  generateESCPOSChineseTestCommands() {
    console.log('生成ESC/POS中文测试指令')

    const commands = []

    // ESC/POS初始化
    commands.push(0x1B, 0x40) // ESC @ - 初始化打印机

    // 设置字符集
    commands.push(0x1C, 0x43, 0x01) // FS C 1 - 选择GB2312字符集

    // 标题
    commands.push(0x1B, 0x61, 0x01) // 居中对齐
    this.pushArray(commands, this.stringToBytes('=== Chinese Test ===\n'))
    commands.push(0x1B, 0x61, 0x00) // 左对齐

    // 测试1: 直接UTF-8编码
    this.pushArray(commands, this.stringToBytes('UTF8: '))
    const utf8Bytes = [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95] // "测试"
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    // 测试2: GBK编码
    this.pushArray(commands, this.stringToBytes('GBK: '))
    const gbkBytes = [0xB2, 0xE2, 0xCA, 0xD4] // "测试"的GBK编码
    this.pushArray(commands, gbkBytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    // 测试3: 设置中文模式
    commands.push(0x1C, 0x26) // FS & - 选择汉字模式
    this.pushArray(commands, this.stringToBytes('Chinese Mode: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))
    commands.push(0x1C, 0x2E) // FS . - 取消汉字模式

    // 测试4: 不同字符集
    commands.push(0x1B, 0x74, 0x00) // ESC t 0 - PC437字符集
    this.pushArray(commands, this.stringToBytes('PC437: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    commands.push(0x1B, 0x74, 0x01) // ESC t 1 - Katakana字符集
    this.pushArray(commands, this.stringToBytes('Katakana: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    commands.push(0x1B, 0x74, 0x03) // ESC t 3 - PC850字符集
    this.pushArray(commands, this.stringToBytes('PC850: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    // 测试5: 中文字体大小
    this.pushArray(commands, this.stringToBytes('Size Test:\n'))

    // 正常中文
    this.pushArray(commands, this.stringToBytes('Normal: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    // 双倍宽度中文
    commands.push(0x1B, 0x21, 0x20) // 双倍宽度
    this.pushArray(commands, this.stringToBytes('Wide: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    // 双倍高度中文
    commands.push(0x1B, 0x21, 0x10) // 双倍高度
    this.pushArray(commands, this.stringToBytes('Tall: '))
    this.pushArray(commands, utf8Bytes)
    this.pushArray(commands, this.stringToBytes('\n'))

    // 恢复正常
    commands.push(0x1B, 0x21, 0x00)

    // 十六进制显示
    this.pushArray(commands, this.stringToBytes('UTF8-HEX: '))
    utf8Bytes.forEach(byte => {
      this.pushArray(commands, this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    this.pushArray(commands, this.stringToBytes('\n'))

    this.pushArray(commands, this.stringToBytes('GBK-HEX: '))
    gbkBytes.forEach(byte => {
      this.pushArray(commands, this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    this.pushArray(commands, this.stringToBytes('\n'))

    // 走纸
    this.pushArray(commands, this.stringToBytes('\n\n\n'))

    return new Uint8Array(commands)
  },

  // 字符串转GBK编码（简化版）
  stringToGBK(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i)
      const code = str.charCodeAt(i)

      if (code < 0x80) {
        // ASCII字符直接输出
        bytes.push(code)
      } else {
        // 中文字符处理
        const gbkBytes = this.chineseToGBK(char)
        if (gbkBytes.length > 0) {
          this.pushArray(bytes, gbkBytes)
        } else {
          // 如果无法转换，使用问号替代
          bytes.push(0x3F) // '?'
        }
      }
    }
    return bytes
  },

  // 简化的中文字符转GBK
  chineseToGBK(char) {
    // 常用中文字符的GBK编码映射（简化版）
    const commonChars = {
      '打': [0xB4, 0xF2],
      '印': [0xD3, 0xA1],
      '测': [0xB2, 0xE2],
      '试': [0xCA, 0xD4],
      '设': [0xC9, 0xE8],
      '备': [0xB1, 0xB8],
      '时': [0xCA, 0xB1],
      '间': [0xBC, 0xE4],
      '纸': [0xD6, 0xBD],
      '张': [0xD5, 0xC5],
      '系': [0xCF, 0xB5],
      '统': [0xCD, 0xB3],
      '成': [0xB3, 0xC9],
      '功': [0xB9, 0xA6],
      '样': [0xD1, 0xF9],
      '品': [0xC6, 0xB7],
      '类': [0xC0, 0xE0],
      '别': [0xB1, 0xF0],
      '编': [0xB1, 0xE0],
      '号': [0xBA, 0xC5],
      '采': [0xB2, 0xC9],
      '日': [0xC8, 0xD5],
      '期': [0xC6, 0xDA],
      '点': [0xB5, 0xE3],
      '位': [0xCE, 0xBB],
      '检': [0xBC, 0xEC],
      '项': [0xCF, 0xEE],
      '目': [0xC4, 0xBF],
      '保': [0xB1, 0xA3],
      '存': [0xB4, 0xE6],
      '容': [0xC8, 0xDD],
      '器': [0xC6, 0xF7],
      '方': [0xB7, 0xBD],
      '式': [0xCA, 0xBD]
    }

    if (commonChars[char]) {
      return commonChars[char]
    }

    // 如果不在常用字符中，尝试使用Unicode转换（简化处理）
    const code = char.charCodeAt(0)
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 中文字符范围，使用简化的双字节编码
      const high = Math.floor((code - 0x4E00) / 256) + 0xA1
      const low = ((code - 0x4E00) % 256) + 0xA1
      return [Math.min(high, 0xFE), Math.min(low, 0xFE)]
    }

    return [] // 无法转换
  },

  // 字符串转UTF-8编码
  stringToUTF8(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i)
      if (code < 0x80) {
        bytes.push(code)
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6))
        bytes.push(0x80 | (code & 0x3F))
      } else {
        bytes.push(0xE0 | (code >> 12))
        bytes.push(0x80 | ((code >> 6) & 0x3F))
        bytes.push(0x80 | (code & 0x3F))
      }
    }
    return bytes
  },

  // 简单的中文字符转UTF-8（常用字符）
  chineseToUTF8(char) {
    const commonChars = {
      '测': [0xE6, 0xB5, 0x8B],
      '试': [0xE8, 0xAF, 0x95],
      '打': [0xE6, 0x89, 0x93],
      '印': [0xE5, 0x8D, 0xB0],
      '样': [0xE6, 0xA0, 0xB7],
      '品': [0xE5, 0x93, 0x81],
      '类': [0xE7, 0xB1, 0xBB],
      '别': [0xE5, 0x88, 0xAB],
      '编': [0xE7, 0xBC, 0x96],
      '号': [0xE5, 0x8F, 0xB7],
      '采': [0xE9, 0x87, 0x87],
      '日': [0xE6, 0x97, 0xA5],
      '期': [0xE6, 0x9C, 0x9F],
      '时': [0xE6, 0x97, 0xB6],
      '间': [0xE9, 0x97, 0xB4],
      '点': [0xE7, 0x82, 0xB9],
      '位': [0xE4, 0xBD, 0x8D],
      '检': [0xE6, 0xA3, 0x80],
      '项': [0xE9, 0xA1, 0xB9],
      '目': [0xE7, 0x9B, 0xAE],
      '保': [0xE4, 0xBF, 0x9D],
      '存': [0xE5, 0xAD, 0x98],
      '容': [0xE5, 0xAE, 0xB9],
      '器': [0xE5, 0x99, 0xA8],
      '方': [0xE6, 0x96, 0xB9],
      '式': [0xE5, 0xBC, 0x8F],
      '状': [0xE7, 0x8A, 0xB6],
      '态': [0xE6, 0x80, 0x81],
      '系': [0xE7, 0xB3, 0xBB],
      '统': [0xE7, 0xBB, 0x9F],
      '成': [0xE6, 0x88, 0x90],
      '功': [0xE5, 0x8A, 0x9F]
    }

    return commonChars[char] || []
  },

  // 字符串转字节数组
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i)
      if (code < 0x80) {
        bytes.push(code)
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6))
        bytes.push(0x80 | (code & 0x3F))
      } else {
        bytes.push(0xE0 | (code >> 12))
        bytes.push(0x80 | ((code >> 6) & 0x3F))
        bytes.push(0x80 | (code & 0x3F))
      }
    }
    return bytes
  },

  // 发送BLE数据
  async sendBLEData(device, data) {
    const maxChunkSize = 20 // BLE每次最大传输20字节

    console.log('开始发送数据，总长度:', data.length, '字节')
    console.log('数据内容:', Array.from(data).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

    let sentBytes = 0

    for (let i = 0; i < data.length; i += maxChunkSize) {
      const chunk = data.slice(i, i + maxChunkSize)

      console.log(`发送第${Math.floor(i/maxChunkSize) + 1}块数据:`, Array.from(chunk).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

      try {
        await wx.writeBLECharacteristicValue({
          deviceId: device.deviceId,
          serviceId: device.serviceId,
          characteristicId: device.characteristicId,
          value: chunk.buffer
        })

        sentBytes += chunk.length
        console.log(`已发送 ${sentBytes}/${data.length} 字节`)

        // 每次发送后稍作延迟，避免数据丢失
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`发送第${Math.floor(i/maxChunkSize) + 1}块数据失败:`, error)
        throw error
      }
    }

    console.log('数据发送完成，总计:', sentBytes, '字节')
  },

  // 发送打印数据（示例）
  async sendPrintData(content) {
    // 这里需要根据具体打印机型号实现
    // 不同品牌的打印机有不同的指令集
    console.log('发送打印数据:', content)
    
    // 模拟发送延迟
    return new Promise(resolve => {
      setTimeout(resolve, 2000)
    })
  },

  // 修改打印设置
  onSettingChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail

    let newValue = value

    // 根据字段类型处理值
    if (field === 'paperWidth') {
      newValue = [58, 75, 80][value]
    } else if (field === 'paperHeight') {
      newValue = [40, 50, 60, 80, 100][value]
    } else if (field === 'printDensity') {
      newValue = ['light', 'medium', 'dark'][value]
    } else if (field === 'printSpeed') {
      newValue = ['slow', 'normal', 'fast'][value]
    } else if (field === 'commandSet') {
      newValue = ['ESC/POS', 'CPCL'][value]
    }

    this.setData({
      [`printerSettings.${field}`]: newValue
    })

    // 保存设置
    wx.setStorageSync('printerSettings', this.data.printerSettings)

    console.log('设置已更新:', field, newValue)
  },

  // 清除所有设置
  clearSettings() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有打印机设置吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('connectedDevice')
          wx.removeStorageSync('printerSettings')

          this.setData({
            connectedDevice: null,
            printerSettings: {
              paperWidth: 75,
              paperHeight: 60,
              printDensity: 'medium',
              printSpeed: 'normal',
              commandSet: 'CPCL'
            }
          })

          wx.showToast({
            title: '设置已清除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 显示使用说明
  showUsageHelp() {
    wx.showModal({
      title: '使用说明',
      content: '1. 确保打印机已开启并处于配对模式\n2. 点击"搜索设备"按钮\n3. 在设备列表中选择您的打印机\n4. 连接成功后进行测试打印\n5. 根据需要调整打印设置\n\n新增功能：\n- 增强中文测试：解决中文乱码问题\n- 自动数据分包：解决传输失败问题\n- 智能格式处理：优化打印布局',
      showCancel: false
    })
  },

  // 专门测试三个核心问题的解决方案
  async testCoreFixes() {
    if (!this.data.connectedDevice) {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: 'CPCL打印测试',
      content: '将使用新的CPCL打印系统进行测试：\n1. 中文编码优化\n2. 数据分包传输\n3. 格式对齐处理\n\n是否开始测试？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 生成测试样品数据
            const testSampleData = {
              sampleId: 'LIMS-TEST-001',
              sampleType: '水样（地下水）',
              location: '采样点A区域',
              date: '2024-01-15',
              items: ['常规检测', '重金属分析', '微生物检测'],
              storage: '冷藏保存（4℃）',
              status: '待测'
            }

            // 使用CPCL打印适配器进行打印
            const result = await this.cpclPrinter.printSample(testSampleData, (progress) => {
              wx.showLoading({
                title: `打印中... ${progress.percentage}%`
              })
            })

            wx.hideLoading()

            if (result.success) {
              wx.showModal({
                title: '测试完成',
                content: 'CPCL打印测试已完成！\n\n请检查打印结果：\n✓ 中文字符是否正确显示\n✓ 所有内容是否完整打印\n✓ 格式布局是否正确对齐\n✓ 二维码是否正常生成',
                showCancel: false
              })
            } else {
              throw new Error(result.message)
            }

          } catch (error) {
            wx.hideLoading()
            console.error('CPCL测试失败:', error)
            wx.showModal({
              title: '测试失败',
              content: `测试过程中出现错误：\n${error.message}`,
              showCancel: false
            })
          }
        }
      }
    })
  },

  // 生成综合测试指令
  generateComprehensiveTestCommands() {
    const commands = []

    // CPCL标签开始
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('! 0 200 200 400 1\r\n'))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('ENCODING GB2312\r\n'))

    // 测试1: 中文乱码问题解决
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('TEXT 3 0 10 10 '))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToGBK('=== 中文乱码测试 ==='))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('\r\n'))

    // 各种中文字符测试
    const chineseTests = [
      '样品类别：水样、土样、气样',
      '检测项目：常规检测、重金属',
      '保存方式：常温、冷藏、冷冻',
      '状态：☐待测 ☐合格 ☐不合格'
    ]

    let yPos = 30
    chineseTests.forEach(text => {
      this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes(`TEXT 2 0 10 ${yPos} `))
      this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToGBK(text))
      this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('\r\n'))
      yPos += 20
    })

    // 测试2: 数据分包传输（通过大量数据测试）
    yPos += 10
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes(`TEXT 3 0 10 ${yPos} `))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToGBK('=== 数据分包测试 ==='))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('\r\n'))

    // 长文本测试数据分包
    yPos += 20
    const longText = '这是一段很长的中文文本用来测试数据分包传输功能是否正常工作包含各种中文字符和标点符号！@#￥%……&*（）'
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes(`TEXT 2 0 10 ${yPos} `))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToGBK(longText))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('\r\n'))

    // 测试3: 格式处理（对齐测试）
    yPos += 40
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes(`TEXT 3 0 10 ${yPos} `))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToGBK('=== 格式对齐测试 ==='))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('\r\n'))

    // 左右对齐测试
    yPos += 20
    const alignTests = [
      ['样品编号', 'LIMS001'],
      ['采样日期', '2024-01-15'],
      ['检测项目', '常规检测'],
      ['保存容器', '500ml塑料瓶']
    ]

    alignTests.forEach(([left, right]) => {
      // 使用格式工具进行对齐
      const alignedText = this.enhancedPrinter.formatUtils.alignLeftRight(left + '：', right, ' ', 20)
      this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes(`TEXT 2 0 10 ${yPos} `))
      this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToGBK(alignedText))
      this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('\r\n'))
      yPos += 20
    })

    // 添加二维码测试
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('BARCODE QR 200 10 M 2 U 4\r\n'))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('MA,LIMS-CORE-TEST-2024\r\n'))
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('ENDQR\r\n'))

    // 打印标签
    this.enhancedPrinter.pushArray(commands, this.enhancedPrinter.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  }
})
