<!--pages/printer/config.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">蓝牙打印机配置</text>
    <text class="page-subtitle">搜索并连接CPCL兼容的蓝牙打印机</text>
  </view>

  <!-- 当前连接状态 -->
  <view class="connection-status card">
    <view class="status-header">
      <text class="status-title">连接状态</text>
      <view class="status-indicator {{connectedDevice ? 'connected' : 'disconnected'}}">
        <text>{{connectedDevice ? '已连接' : '未连接'}}</text>
      </view>
    </view>
    
    <view wx:if="{{connectedDevice}}" class="connected-device">
      <view class="device-info">
        <text class="device-name">{{connectedDevice.name}}</text>
        <text class="device-id">{{connectedDevice.deviceId}}</text>
      </view>
      <view class="device-actions">
        <button class="btn-primary" bindtap="testCoreFixes">核心问题测试</button>
        <button class="btn-secondary" bindtap="testPrint">常规测试</button>
        <button class="btn-danger" bindtap="disconnectPrinter">断开连接</button>
      </view>
    </view>
    
    <view wx:else class="no-connection">
      <text class="no-connection-text">暂未连接打印机</text>
    </view>
  </view>

  <!-- 搜索蓝牙设备 -->
  <view class="search-section card">
    <view class="section-header">
      <text class="section-title">搜索设备</text>
      <button
        class="btn-primary {{isScanning ? 'loading' : ''}}"
        bindtap="searchBluetoothDevices"
        disabled="{{isScanning}}"
      >
        {{isScanning ? '搜索中...' : '搜索设备'}}
      </button>
    </view>

    <view class="search-tips">
      <text class="tips-text">• 确保打印机已开启并处于配对模式</text>
      <text class="tips-text">• 支持CPCL指令集的标签打印机</text>
      <text class="tips-text">• 搜索时间10秒，会显示所有蓝牙设备</text>
      <text class="tips-text">• 可以尝试连接任何可疑的设备</text>
    </view>
  </view>

  <!-- 设备列表 -->
  <view wx:if="{{allDevices.length > 0}}" class="device-list card">
    <view class="section-header">
      <text class="section-title">发现的设备 ({{allDevices.length}})</text>
      <text class="debug-tip">可点击任意设备尝试连接</text>
    </view>

    <view class="device-items">
      <view
        wx:for="{{allDevices}}"
        wx:key="deviceId"
        class="device-item"
        bindtap="connectPrinter"
        data-device="{{item}}"
      >
        <view class="device-main">
          <view class="device-icon">
            <image src="/static/icons/printer.png" mode="aspectFit"></image>
          </view>
          <view class="device-details">
            <text class="device-name">{{item.displayName || item.name || '无名称设备'}}</text>
            <text class="device-id">ID: {{item.deviceId.slice(-8)}}</text>
            <text class="device-rssi">信号: {{item.RSSI}}dBm</text>
            <text class="device-type">{{item.name ? '有名称' : '无名称'}} BLE设备</text>
            <text wx:if="{{item.advertisData}}" class="device-adv">有广播数据</text>
          </view>
        </view>
        <view class="device-connect">
          <text class="connect-text">尝试连接</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 打印设置 -->
  <view class="print-settings card">
    <view class="section-header">
      <text class="section-title">打印设置</text>
    </view>
    
    <view class="settings-list">
      <!-- 纸张宽度 -->
      <view class="setting-item">
        <text class="setting-label">纸张宽度 (mm)</text>
        <picker 
          mode="selector" 
          range="{{[58, 75, 80]}}" 
          value="{{printerSettings.paperWidth === 58 ? 0 : printerSettings.paperWidth === 75 ? 1 : 2}}"
          bindchange="onSettingChange"
          data-field="paperWidth"
        >
          <view class="setting-value">
            {{printerSettings.paperWidth}}mm
          </view>
        </picker>
      </view>
      
      <!-- 纸张高度 -->
      <view class="setting-item">
        <text class="setting-label">纸张高度 (mm)</text>
        <picker 
          mode="selector" 
          range="{{[40, 50, 60, 80, 100]}}" 
          value="{{printerSettings.paperHeight === 40 ? 0 : printerSettings.paperHeight === 50 ? 1 : printerSettings.paperHeight === 60 ? 2 : printerSettings.paperHeight === 80 ? 3 : 4}}"
          bindchange="onSettingChange"
          data-field="paperHeight"
        >
          <view class="setting-value">
            {{printerSettings.paperHeight}}mm
          </view>
        </picker>
      </view>
      
      <!-- 打印密度 -->
      <view class="setting-item">
        <text class="setting-label">打印密度</text>
        <picker 
          mode="selector" 
          range="{{['light', 'medium', 'dark']}}" 
          range-key="text"
          value="{{printerSettings.printDensity === 'light' ? 0 : printerSettings.printDensity === 'medium' ? 1 : 2}}"
          bindchange="onSettingChange"
          data-field="printDensity"
        >
          <view class="setting-value">
            {{printerSettings.printDensity === 'light' ? '浅' : printerSettings.printDensity === 'medium' ? '中' : '深'}}
          </view>
        </picker>
      </view>
      
      <!-- 打印速度 -->
      <view class="setting-item">
        <text class="setting-label">打印速度</text>
        <picker
          mode="selector"
          range="{{['slow', 'normal', 'fast']}}"
          value="{{printerSettings.printSpeed === 'slow' ? 0 : printerSettings.printSpeed === 'normal' ? 1 : 2}}"
          bindchange="onSettingChange"
          data-field="printSpeed"
        >
          <view class="setting-value">
            {{printerSettings.printSpeed === 'slow' ? '慢' : printerSettings.printSpeed === 'normal' ? '正常' : '快'}}
          </view>
        </picker>
      </view>

      <!-- 指令集显示 -->
      <view class="setting-item">
        <text class="setting-label">指令集</text>
        <view class="setting-value readonly">
          CPCL (固定)
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="btn-secondary" bindtap="clearSettings">清除设置</button>
    <button class="btn-info" bindtap="showUsageHelp">使用说明</button>
  </view>

  <!-- 使用说明 -->
  <view class="help-section card">
    <view class="section-header">
      <text class="section-title">使用说明</text>
    </view>
    
    <view class="help-content">
      <text class="help-text">1. 确保打印机已开启电源并处于配对模式</text>
      <text class="help-text">2. 点击"搜索设备"按钮搜索附近的蓝牙设备</text>
      <text class="help-text">3. 在设备列表中选择您的打印机设备</text>
      <text class="help-text">4. 可以尝试连接任何可疑的设备进行测试</text>
      <text class="help-text">5. 连接成功后进行测试打印验证功能</text>
      <text class="help-text">6. 根据实际标签纸规格调整打印设置</text>
      <text class="help-text">7. 配置完成后即可在样品管理中使用打印功能</text>
      <text class="help-text">8. 系统默认使用CPCL指令集，适合标签打印机</text>
      <text class="help-text" style="color: #007AFF; font-weight: bold;">🆕 核心问题测试：专门测试中文乱码、数据分包、格式处理问题的解决方案</text>
    </view>
  </view>
</view>
