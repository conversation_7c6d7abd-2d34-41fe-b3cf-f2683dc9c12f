<!--pages/sampling/task-detail.wxml-->
<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="header-title">采样任务</view>
    <view wx:if="{{task.status >= 1}}" class="task-number">分组编号：{{task.groupCode || task.code || 'GROUP001'}}</view>
    <view wx:if="{{task.status < 1}}" class="task-number">任务编号：{{task.taskCode || task.code || '-'}}</view>
    <view class="task-status-header">
      <text class="status-label">任务状态：</text>
      <view class="status-badge status-{{task.status || 0}}">
        <text>{{task.statusText || '待执行'}}</text>
      </view>
    </view>
  </view>

  <!-- Tab切换 -->
  <view class="tab-container">
    <view class="tabs">
      <view
        class="tab-item {{activeTab === 'task' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="task"
      >
        任务信息
      </view>
      <view
        wx:if="{{task.status >= 1}}"
        class="tab-item {{activeTab === 'samples' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="samples"
      >
        样品管理
      </view>
      <view
        wx:if="{{task.status >= 1}}"
        class="tab-item {{activeTab === 'point' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="point"
      >
        点位信息
      </view>
    </view>

    <!-- Tab内容 -->
    <view class="tab-content">
      <!-- 任务信息 -->
      <view wx:if="{{activeTab === 'task'}}" class="content">
        <view class="section">
          <view class="section-title">基础信息</view>
          <view class="info-grid">
            <view wx:if="{{task.status >= 1}}" class="info-item">
              <text class="label">分组编号：</text>
              <text class="value">{{task.groupCode || task.code || '-'}}</text>
            </view>
            <view wx:if="{{task.status < 1}}" class="info-item">
              <text class="label">任务编号：</text>
              <text class="value">{{task.taskCode || task.code || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">任务名称：</text>
              <text class="value">{{task.taskName || task.title || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">项目名称：</text>
              <text class="value">{{task.projectName || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">项目编号：</text>
              <text class="value">{{task.projectCode || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">客户名称：</text>
              <text class="value">{{task.customerName || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">周期序号：</text>
              <text class="value">{{task.cycleNumber || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">周期类型：</text>
              <text class="value">{{task.cycleType || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">检测类别：</text>
              <text class="value">{{task.detectionCategory || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">点位名称：</text>
              <text class="value">{{task.pointName || '-'}}</text>
            </view>
          </view>
        </view>

        <!-- 合同信息 -->
        <view class="section">
          <view class="section-title">合同信息</view>
          <view class="info-grid">
            <view class="info-item">
              <text class="label">合同名称：</text>
              <text class="value">{{task.contractName || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">合同编号：</text>
              <text class="value">{{task.contractNumber || '-'}}</text>
            </view>
          </view>
        </view>

        <!-- 方案详情 -->
        <view class="section">
          <view class="section-title">方案详情</view>
          <view class="info-grid">
            <view class="info-item">
              <text class="label">频次：</text>
              <text class="value">{{task.schemeDetails.frequency || 0}}</text>
            </view>
            <view class="info-item">
              <text class="label">样品数：</text>
              <text class="value">{{task.schemeDetails.sampleCount || 0}}</text>
            </view>
            <view class="info-item">
              <text class="label">周期数：</text>
              <text class="value">{{task.schemeDetails.cycleCount || 0}}</text>
            </view>
            <view class="info-item">
              <text class="label">点位数：</text>
              <text class="value">{{task.schemeDetails.pointCount || 0}}</text>
            </view>
          </view>
        </view>

        <!-- 任务操作 -->
        <view class="section">
          <view class="section-title">任务操作</view>
          <view class="task-actions">
            <!-- 开始执行按钮 -->
            <button
              wx:if="{{task.status === 0}}"
              class="action-btn start-btn"
              bindtap="startTask"
            >
              开始执行
            </button>

            <!-- 完成任务按钮 -->
            <button
              wx:if="{{task.status === 1}}"
              class="action-btn complete-btn"
              bindtap="completeTask"
            >
              完成任务
            </button>

            <!-- 任务已完成提示 -->
            <view wx:if="{{task.status === 2}}" class="task-completed">
              <text class="completed-text">✓ 任务已完成</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 样品管理 -->
      <view wx:if="{{activeTab === 'samples'}}" class="content">
        <!-- 样品统计 -->
        <view class="section">
          <view class="section-title">样品统计</view>
          <view class="statistics-grid">
            <view class="stat-item">
              <view class="stat-number">{{sampleStatistics.totalCount || 0}}</view>
              <view class="stat-label">总数量</view>
            </view>
            <view class="stat-item pending">
              <view class="stat-number">{{sampleStatistics.pendingCount || 0}}</view>
              <view class="stat-label">待采集</view>
            </view>
            <view class="stat-item collecting">
              <view class="stat-number">{{sampleStatistics.collectingCount || 0}}</view>
              <view class="stat-label">采集中</view>
            </view>
            <view class="stat-item collected">
              <view class="stat-number">{{sampleStatistics.collectedCount || 0}}</view>
              <view class="stat-label">已采集</view>
            </view>
            <view class="stat-item submitted">
              <view class="stat-number">{{sampleStatistics.submittedCount || 0}}</view>
              <view class="stat-label">已提交</view>
            </view>
          </view>
        </view>

        <!-- 样品记录列表 -->
        <view class="section">
          <view class="section-title">样品记录</view>
          <view wx:if="{{sampleList.length === 0}}" class="empty-state">
            <image class="empty-icon" src="/static/icons/empty.png" mode="aspectFit"></image>
            <text class="empty-text">暂无样品记录</text>
            <text class="empty-tip">请确保网络连接正常，或联系管理员</text>
          </view>
          <view wx:else class="sample-list">
            <view
              wx:for="{{sampleList}}"
              wx:key="id"
              class="sample-item"
            >
              <view class="sample-header">
                <view class="sample-number">样品序号：{{item.sampleNumber || (index + 1)}}</view>
                <view class="sample-status status-{{item.status}}">
                  <text>{{item.statusText || '待采集'}}</text>
                </view>
              </view>

              <view class="sample-info">
                <view class="info-row">
                  <text class="label">样品类型：</text>
                  <text class="value">{{item.sampleType || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">样品来源：</text>
                  <text class="value">{{item.sampleSource || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">点位名称：</text>
                  <text class="value">{{item.pointName || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">采样周期：</text>
                  <text class="value">{{item.cycleNumber}}({{item.cycleType}})</text>
                </view>
                <view class="info-row">
                  <text class="label">检测类别：</text>
                  <text class="value">{{item.detectionCategory || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">检测参数：</text>
                  <text class="value">{{item.detectionParameter || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">检测方法：</text>
                  <text class="value">{{item.detectionMethod || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">采集时间：</text>
                  <text class="value">{{item.collectionTime || '-'}}</text>
                </view>
              </view>

              <!-- 移除样品操作按钮，样品状态由瓶组状态自动计算 -->

              <!-- 瓶组信息 -->
              <view wx:if="{{item.bottleGroups && item.bottleGroups.length > 0}}" class="bottle-groups">
                <view class="bottle-groups-title">关联瓶组</view>
                <view class="bottle-list">
                  <view
                    wx:for="{{item.bottleGroups}}"
                    wx:for-item="bottle"
                    wx:key="id"
                    class="bottle-item"
                  >
                    <view class="bottle-info">
                      <view class="bottle-name">{{bottle.bottleGroupCode || ('瓶组' + bottle.id)}}</view>
                      <view class="bottle-status status-{{bottle.status}}">
                        <text wx:if="{{bottle.status === 0}}">采样</text>
                        <text wx:elif="{{bottle.status === 1}}">装箱</text>
                        <text wx:elif="{{bottle.status === 2}}">流转</text>
                        <text wx:elif="{{bottle.status === 3}}">完成</text>
                        <text wx:else>未知</text>
                      </view>
                    </view>
                    <view class="bottle-details">
                      <view class="bottle-detail-row">
                        <text class="detail-label">类型：</text>
                        <text class="detail-value">{{bottle.bottleType || '默认瓶组'}}</text>
                      </view>
                      <view class="bottle-detail-row">
                        <text class="detail-label">容量：</text>
                        <text class="detail-value">{{bottle.bottleVolume || '-'}}</text>
                      </view>
                      <view class="bottle-detail-row">
                        <text class="detail-label">检测方法：</text>
                        <text class="detail-value">{{bottle.detectionMethod || '-'}}</text>
                      </view>
                    </view>
                    <view class="bottle-actions">
                      <!-- 状态流转按钮 -->
                      <button
                        wx:if="{{bottle.status < 3}}"
                        class="sample-action-btn primary"
                        bindtap="handleBottleStatusUpdate"
                        data-bottle="{{bottle}}"
                        data-status="{{bottle.status + 1}}"
                      >
                        {{bottle.status === 0 ? '装箱' : bottle.status === 1 ? '流转' : '完成'}}
                      </button>

                      <!-- 回退按钮 -->
                      <button
                        wx:if="{{bottle.status > 0}}"
                        class="sample-action-btn warning"
                        bindtap="handleBottleStatusUpdate"
                        data-bottle="{{bottle}}"
                        data-status="{{bottle.status - 1}}"
                      >
                        回退
                      </button>

                      <!-- 打印按钮 -->
                      <button
                        class="sample-action-btn info"
                        bindtap="handleBottlePrint"
                        data-bottle="{{bottle}}"
                      >
                        打印
                      </button>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 加载瓶组按钮 -->
              <view wx:if="{{!item.bottleGroupsLoaded}}" class="load-bottles">
                <button
                  class="load-bottles-btn"
                  bindtap="loadBottleGroups"
                  data-sample="{{item}}"
                >
                  查看瓶组信息
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 点位信息 -->
      <view wx:if="{{activeTab === 'point'}}" class="content">
        <!-- 加载状态 -->
        <view wx:if="{{pointInfoLoading}}" class="loading-section">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载点位信息中...</text>
        </view>

        <!-- 点位信息内容 -->
        <view wx:else>
          <!-- 基本信息 -->
          <view class="section">
            <view class="section-title">基本信息</view>
            <view class="form-grid">
              <view class="form-item">
                <text class="form-label">采样时间</text>
                <picker
                  mode="multiSelector"
                  range="{{dateTimeRange}}"
                  value="{{dateTimeValue}}"
                  bindchange="onSamplingTimeChange"
                  class="form-picker"
                >
                  <view class="picker-display">
                    {{pointInfo.samplingTime || '请选择采样时间'}}
                  </view>
                </picker>
              </view>
            </view>
          </view>

          <!-- 位置信息 -->
          <view class="section">
            <view class="section-title">
              位置信息
              <button
                class="location-btn {{locationLoading ? 'loading' : ''}}"
                bindtap="getCurrentLocation"
                disabled="{{locationLoading}}"
              >
                {{locationLoading ? '获取中...' : '获取位置'}}
              </button>
            </view>

            <!-- 位置获取提示 -->
            <view class="location-tips">
              <view class="tips-content">
                <text class="tip-item">• 点击"获取位置"按钮自动获取当前坐标</text>
                <text class="tip-item">• 需要允许小程序访问位置权限</text>
                <text class="tip-item">• 也可以手动输入经纬度坐标</text>
              </view>
            </view>

            <view class="form-grid">
              <view class="form-item">
                <text class="form-label">经度</text>
                <input
                  class="form-input"
                  type="digit"
                  value="{{pointInfo.longitude}}"
                  placeholder="请输入经度"
                  bindinput="onLongitudeInput"
                  bindblur="savePointInfo"
                />
              </view>
              <view class="form-item">
                <text class="form-label">纬度</text>
                <input
                  class="form-input"
                  type="digit"
                  value="{{pointInfo.latitude}}"
                  placeholder="请输入纬度"
                  bindinput="onLatitudeInput"
                  bindblur="savePointInfo"
                />
              </view>
              <view class="form-item">
                <text class="form-label">海拔(米)</text>
                <input
                  class="form-input"
                  type="number"
                  value="{{pointInfo.altitude}}"
                  placeholder="请输入海拔"
                  bindinput="onAltitudeInput"
                  bindblur="savePointInfo"
                />
              </view>
            </view>

            <!-- 坐标显示 -->
            <view wx:if="{{pointInfo.longitude && pointInfo.latitude}}" class="coordinate-display">
              <view class="coordinate-tag">
                <text class="coordinate-icon">📍</text>
                <text class="coordinate-text">
                  坐标: {{pointInfo.longitude}}, {{pointInfo.latitude}}
                  <text wx:if="{{pointInfo.altitude}}"> (海拔: {{pointInfo.altitude}}米)</text>
                </text>
              </view>
            </view>
          </view>

          <!-- 环境信息 -->
          <view class="section">
            <view class="section-title">环境信息</view>
            <view class="form-grid">
              <view class="form-item">
                <text class="form-label">天气状况</text>
                <picker
                  range="{{weatherOptions}}"
                  value="{{weatherIndex}}"
                  bindchange="onWeatherChange"
                  class="form-picker"
                >
                  <view class="picker-display">
                    {{pointInfo.weatherCondition || '请选择天气状况'}}
                  </view>
                </picker>
              </view>
              <view class="form-item">
                <text class="form-label">温度(℃)</text>
                <input
                  class="form-input"
                  type="digit"
                  value="{{pointInfo.temperature}}"
                  placeholder="请输入温度"
                  bindinput="onTemperatureInput"
                  bindblur="savePointInfo"
                />
              </view>
              <view class="form-item">
                <text class="form-label">湿度(%)</text>
                <input
                  class="form-input"
                  type="number"
                  value="{{pointInfo.humidity}}"
                  placeholder="请输入湿度"
                  bindinput="onHumidityInput"
                  bindblur="savePointInfo"
                />
              </view>
              <view class="form-item">
                <text class="form-label">风速(m/s)</text>
                <input
                  class="form-input"
                  type="digit"
                  value="{{pointInfo.windSpeed}}"
                  placeholder="请输入风速"
                  bindinput="onWindSpeedInput"
                  bindblur="savePointInfo"
                />
              </view>
              <view class="form-item">
                <text class="form-label">风向</text>
                <picker
                  range="{{windDirectionOptions}}"
                  value="{{windDirectionIndex}}"
                  bindchange="onWindDirectionChange"
                  class="form-picker"
                >
                  <view class="picker-display">
                    {{pointInfo.windDirection || '请选择风向'}}
                  </view>
                </picker>
              </view>
            </view>
          </view>

          <!-- 点位照片 -->
          <view class="section">
            <view class="section-title">点位照片</view>
            <view class="photo-upload-area">
              <view class="photo-item">
                <text class="photo-label">点位照片</text>
                <view class="photo-container">
                  <image
                    wx:if="{{pointInfo.pointPhotoUrl}}"
                    class="photo-preview"
                    src="{{getImageUrl(pointInfo.pointPhotoUrl)}}"
                    mode="aspectFill"
                    bindtap="previewImage"
                    data-url="{{getImageUrl(pointInfo.pointPhotoUrl)}}"
                  />
                  <view
                    wx:else
                    class="photo-placeholder"
                    bindtap="uploadPointPhoto"
                    data-type="point"
                  >
                    <text class="upload-icon">📷</text>
                    <text class="upload-text">点击上传</text>
                  </view>
                  <view wx:if="{{pointInfo.pointPhotoUrl}}" class="photo-actions">
                    <button
                      class="photo-action-btn"
                      bindtap="uploadPointPhoto"
                      data-type="point"
                    >
                      重新上传
                    </button>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 环境照片 -->
          <view class="section">
            <view class="section-title">环境照片</view>
            <view class="environment-photos">
              <view class="photo-grid">
                <!-- 东侧照片 -->
                <view class="photo-item">
                  <text class="photo-label">东侧</text>
                  <view class="photo-container">
                    <image
                      wx:if="{{pointInfo.eastPhotoUrl}}"
                      class="photo-preview"
                      src="{{getImageUrl(pointInfo.eastPhotoUrl)}}"
                      mode="aspectFill"
                      bindtap="previewImage"
                      data-url="{{getImageUrl(pointInfo.eastPhotoUrl)}}"
                    />
                    <view
                      wx:else
                      class="photo-placeholder"
                      bindtap="uploadEnvironmentPhoto"
                      data-direction="east"
                    >
                      <text class="upload-icon">📷</text>
                      <text class="upload-text">东侧</text>
                    </view>
                    <view wx:if="{{pointInfo.eastPhotoUrl}}" class="photo-actions">
                      <button
                        class="photo-action-btn"
                        bindtap="uploadEnvironmentPhoto"
                        data-direction="east"
                      >
                        重新上传
                      </button>
                    </view>
                  </view>
                </view>

                <!-- 南侧照片 -->
                <view class="photo-item">
                  <text class="photo-label">南侧</text>
                  <view class="photo-container">
                    <image
                      wx:if="{{pointInfo.southPhotoUrl}}"
                      class="photo-preview"
                      src="{{getImageUrl(pointInfo.southPhotoUrl)}}"
                      mode="aspectFill"
                      bindtap="previewImage"
                      data-url="{{getImageUrl(pointInfo.southPhotoUrl)}}"
                    />
                    <view
                      wx:else
                      class="photo-placeholder"
                      bindtap="uploadEnvironmentPhoto"
                      data-direction="south"
                    >
                      <text class="upload-icon">📷</text>
                      <text class="upload-text">南侧</text>
                    </view>
                    <view wx:if="{{pointInfo.southPhotoUrl}}" class="photo-actions">
                      <button
                        class="photo-action-btn"
                        bindtap="uploadEnvironmentPhoto"
                        data-direction="south"
                      >
                        重新上传
                      </button>
                    </view>
                  </view>
                </view>

                <!-- 西侧照片 -->
                <view class="photo-item">
                  <text class="photo-label">西侧</text>
                  <view class="photo-container">
                    <image
                      wx:if="{{pointInfo.westPhotoUrl}}"
                      class="photo-preview"
                      src="{{getImageUrl(pointInfo.westPhotoUrl)}}"
                      mode="aspectFill"
                      bindtap="previewImage"
                      data-url="{{getImageUrl(pointInfo.westPhotoUrl)}}"
                    />
                    <view
                      wx:else
                      class="photo-placeholder"
                      bindtap="uploadEnvironmentPhoto"
                      data-direction="west"
                    >
                      <text class="upload-icon">📷</text>
                      <text class="upload-text">西侧</text>
                    </view>
                    <view wx:if="{{pointInfo.westPhotoUrl}}" class="photo-actions">
                      <button
                        class="photo-action-btn"
                        bindtap="uploadEnvironmentPhoto"
                        data-direction="west"
                      >
                        重新上传
                      </button>
                    </view>
                  </view>
                </view>

                <!-- 北侧照片 -->
                <view class="photo-item">
                  <text class="photo-label">北侧</text>
                  <view class="photo-container">
                    <image
                      wx:if="{{pointInfo.northPhotoUrl}}"
                      class="photo-preview"
                      src="{{getImageUrl(pointInfo.northPhotoUrl)}}"
                      mode="aspectFill"
                      bindtap="previewImage"
                      data-url="{{getImageUrl(pointInfo.northPhotoUrl)}}"
                    />
                    <view
                      wx:else
                      class="photo-placeholder"
                      bindtap="uploadEnvironmentPhoto"
                      data-direction="north"
                    >
                      <text class="upload-icon">📷</text>
                      <text class="upload-text">北侧</text>
                    </view>
                    <view wx:if="{{pointInfo.northPhotoUrl}}" class="photo-actions">
                      <button
                        class="photo-action-btn"
                        bindtap="uploadEnvironmentPhoto"
                        data-direction="north"
                      >
                        重新上传
                      </button>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 备注信息 -->
          <view class="section">
            <view class="section-title">备注信息</view>
            <view class="form-item full-width">
              <textarea
                class="form-textarea"
                value="{{pointInfo.remarks}}"
                placeholder="请输入备注信息"
                bindinput="onRemarksInput"
                bindblur="savePointInfo"
                maxlength="500"
                show-confirm-bar="{{false}}"
              />
              <view class="textarea-counter">{{pointInfo.remarks ? pointInfo.remarks.length : 0}}/500</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text>扫码时间：{{currentTime}}</text>
    <text>采样管理系统</text>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
