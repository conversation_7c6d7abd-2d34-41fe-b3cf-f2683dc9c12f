// pages/sampling/task-detail.js
const app = getApp()
const { TextEncoder, TextDecoder } = require('../../utils/text-encoding.js')

Page({
  // 辅助函数：替代扩展运算符
  pushArray: function(target, source) {
    for (let i = 0; i < source.length; i++) {
      target.push(source[i])
    }
  },

  data: {
    taskId: '',
    task: {},
    sampleList: [],
    sampleStatistics: {
      totalCount: 0,
      pendingCount: 0,
      collectingCount: 0,
      collectedCount: 0,
      submittedCount: 0
    },
    loading: true,
    activeTab: 'task', // 当前激活的Tab
    currentTime: '',

    // 点位信息相关数据
    pointInfoLoading: false,
    locationLoading: false,
    pointInfo: {
      samplingTime: '',
      longitude: '',
      latitude: '',
      altitude: '',
      weatherCondition: '',
      temperature: '',
      humidity: '',
      windSpeed: '',
      windDirection: '',
      pointPhotoUrl: '',
      eastPhotoUrl: '',
      southPhotoUrl: '',
      westPhotoUrl: '',
      northPhotoUrl: '',
      remarks: ''
    },

    // 选择器数据
    weatherOptions: ['晴', '多云', '阴', '小雨', '中雨', '大雨', '雪', '雾', '霾'],
    weatherIndex: -1,
    windDirectionOptions: ['北风', '东北风', '东风', '东南风', '南风', '西南风', '西风', '西北风'],
    windDirectionIndex: -1,

    // 时间选择器数据
    dateTimeRange: [[], [], [], []], // 年、月、日、时分
    dateTimeValue: [0, 0, 0, 0]
  },

  onLoad(options) {
    console.log('任务详情页面加载')
    const taskId = options.id
    if (taskId) {
      this.setData({
        taskId,
        currentTime: new Date().toLocaleString()
      })
      this.loadTaskDetail(taskId)
      // 记录任务打开历史
      this.recordTaskHistory(taskId)
    } else {
      this.loadMockData()
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
  },

  onPullDownRefresh() {
    this.loadTaskDetail(this.data.taskId).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 记录任务打开历史
  recordTaskHistory(taskId) {
    try {
      console.log('开始记录任务历史:', taskId)

      // 获取现有历史记录
      let history = wx.getStorageSync('recentTaskHistory') || []
      console.log('当前历史记录数量:', history.length)

      // 移除重复的任务ID（如果存在）
      const beforeLength = history.length
      history = history.filter(item => item.taskId !== taskId)
      if (beforeLength !== history.length) {
        console.log('移除了重复的任务记录')
      }

      // 添加新记录到开头
      history.unshift({
        taskId: taskId,
        openTime: new Date().getTime()
      })
      console.log('新记录已添加到开头')

      // 只保留最近5条记录
      if (history.length > 5) {
        const removed = history.length - 5
        history = history.slice(0, 5)
        console.log('移除了', removed, '条旧记录')
      }

      // 保存到本地存储
      wx.setStorageSync('recentTaskHistory', history)
      console.log('任务历史已保存，当前记录数:', history.length)

      // 验证保存结果
      const saved = wx.getStorageSync('recentTaskHistory')
      console.log('验证保存结果:', saved.length, '条记录')

    } catch (error) {
      console.error('记录任务历史失败:', error)
    }
  },

  // 加载任务详情
  async loadTaskDetail(taskId) {
    this.setData({ loading: true })

    try {
      // 先加载任务基本信息
      const taskData = await this.loadTaskInfo(taskId)

      // 根据任务状态决定是否加载样品数据
      let samplesData = []
      let statistics = {
        totalCount: 0,
        pendingCount: 0,
        collectingCount: 0,
        collectedCount: 0,
        submittedCount: 0
      }

      // 只有任务状态为执行中(1)或已完成(2)时才加载样品数据
      if (taskData.status >= 1) {
        samplesData = await this.loadSampleList(taskId)
        statistics = this.calculateSampleStatistics(samplesData)
      }

      this.setData({
        task: taskData,
        sampleList: samplesData,
        sampleStatistics: statistics
      })

      // 更新历史记录中的任务信息
      this.updateTaskHistoryInfo(taskId, taskData)

    } catch (error) {
      console.error('加载任务详情失败:', error)
      app.showToast('加载失败，请重试')
      this.loadMockData()
    } finally {
      this.setData({ loading: false })
    }
  },

  // 更新历史记录中的任务信息
  updateTaskHistoryInfo(taskId, taskData) {
    try {
      let history = wx.getStorageSync('recentTaskHistory') || []

      // 找到对应的历史记录并更新信息
      const index = history.findIndex(item => item.taskId === taskId)
      if (index !== -1) {
        // 确保获取正确的任务名称
        const taskTitle = taskData.title || taskData.taskName || taskData.projectName || '未命名任务'
        const taskLocation = taskData.location || taskData.samplingLocation || taskData.pointName || '未指定'

        console.log('更新任务历史信息:', {
          taskId,
          title: taskTitle,
          location: taskLocation,
          status: taskData.status
        })

        history[index] = Object.assign({}, history[index], {
          title: taskTitle,
          location: taskLocation,
          status: taskData.status,
          statusText: this.getStatusText(taskData.status),
          completedSamples: taskData.completedSamples || 0,
          totalSamples: taskData.totalSamples || 0,
          progress: taskData.progress || 0
        })

        wx.setStorageSync('recentTaskHistory', history)
        console.log('任务历史信息已更新')
      } else {
        console.warn('未找到对应的历史记录:', taskId)
      }
    } catch (error) {
      console.error('更新任务历史信息失败:', error)
    }
  },

  // 加载任务分组信息 - 这里的taskId实际上是groupId
  loadTaskInfo(groupId) {
    return app.request({
      url: `/sampling/task-group/${groupId}`, // 使用分组详情接口
      method: 'GET'
    }).then(result => {
      if (result.code === 200) {
        const data = result.data
        return Object.assign({}, data, {
          // 映射字段名以匹配前端显示
          title: data.taskName || '未命名任务',
          code: data.groupCode || data.taskCode || '无编号',
          location: data.samplingLocation || '未指定',
          assignee: data.responsibleUserName || '未分配',
          planDate: this.formatDate(data.plannedStartDate || data.plannedEndDate),
          // 状态和进度
          statusText: this.getStatusText(data.status),
          progress: 0, // 暂时设为0
          // 分组特有信息
          groupCode: data.groupCode,
          projectName: data.projectName,
          projectCode: data.projectCode, // 项目编号
          customerName: data.customerName,
          cycleNumber: data.cycleNumber,
          cycleType: data.cycleType,
          detectionCategory: data.detectionCategory,
          pointName: data.pointName,
          // 合同信息
          contractName: data.contractName,
          contractNumber: data.contractNumber,
          // 方案详情
          schemeDetails: data.schemeDetails || {
            frequency: 0,
            sampleCount: 0,
            cycleCount: 0,
            pointCount: 0
          }
        })
      } else {
        throw new Error(result.message || '获取任务信息失败')
      }
    }).catch(error => {
      console.error('加载任务信息失败:', error)
      throw error
    })
  },

  // 加载样品列表 - 根据分组ID获取样品记录
  async loadSampleList(groupId) {
    try {
      // 先尝试获取现有的样品记录
      const result = await app.request({
        url: `/sampling/sample-records/group/${groupId}`, // 使用正确的接口路径
        method: 'GET'
      })

      if (result.code === 200) {
        let sampleList = result.data || []

        // 如果没有样品记录，尝试生成
        if (sampleList.length === 0) {
          console.log('没有找到样品记录，尝试生成...')
          try {
            const generateResult = await app.request({
              url: `/sampling/sample-records/generate/group/${groupId}`,
              method: 'POST'
            })

            if (generateResult.code === 200) {
              sampleList = generateResult.data || []
              console.log(`成功生成 ${sampleList.length} 条样品记录`)
            }
          } catch (generateError) {
            console.warn('生成样品记录失败:', generateError)
            // 生成失败不影响页面显示，继续使用空列表
          }
        }

        return sampleList.map(sample => Object.assign({}, sample, {
          statusText: this.getSampleStatusText(sample.status),
          bottleGroupsLoaded: false,
          bottleGroupsLoading: false,
          bottleGroups: []
        }))
      } else {
        console.warn('获取样品列表失败:', result.message)
        return []
      }
    } catch (error) {
      console.error('加载样品列表失败:', error)
      return []
    }
  },

  // 加载模拟数据
  loadMockData() {
    const mockTask = {
      id: 1,
      title: '水质采样任务-001',
      code: 'WQ20240115001',
      location: '北京市朝阳区某工厂排污口',
      status: 'in_progress',
      statusText: '进行中',
      assignee: '张三',
      planDate: '01-15',
      completedSamples: 2,
      totalSamples: 5,
      progress: 40
    }

    const mockSamples = [
      {
        id: 1,
        name: '进水口水样',
        code: 'WS001',
        type: '地表水',
        status: 1,
        statusText: '已采集'
      },
      {
        id: 2,
        name: '出水口水样',
        code: 'WS002',
        type: '地表水',
        status: 1,
        statusText: '已采集'
      },
      {
        id: 3,
        name: '中段水样',
        code: 'WS003',
        type: '地表水',
        status: 0,
        statusText: '待采集'
      }
    ]

    // 计算模拟数据的统计信息
    const statistics = this.calculateSampleStatistics(mockSamples)

    this.setData({
      task: mockTask,
      sampleList: mockSamples,
      sampleStatistics: statistics,
      loading: false
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待执行',
      1: '执行中',
      2: '已完成',
      3: '已取消',
      // 兼容字符串状态
      'pending': '待执行',
      'in_progress': '执行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知'
  },

  // 根据瓶组状态计算样品状态
  calculateSampleStatusFromBottles(bottleGroups) {
    if (!bottleGroups || bottleGroups.length === 0) {
      return 0 // 没有瓶组，默认待采集
    }

    // 统计各状态的瓶组数量
    const statusCounts = {
      0: 0, // 采样
      1: 0, // 装箱
      2: 0, // 流转
      3: 0  // 完成
    }

    bottleGroups.forEach(bottle => {
      const status = bottle.status || 0
      if (statusCounts.hasOwnProperty(status)) {
        statusCounts[status]++
      }
    })

    const totalBottles = bottleGroups.length

    // 状态计算逻辑：
    // 1. 所有瓶组都是采样状态(0) -> 待采集
    // 2. 所有瓶组都已完成流转(3) -> 已提交
    // 3. 所有瓶组都已完成采样(1,2,3) -> 已采集
    // 4. 部分瓶组已开始流转 -> 采集中

    if (statusCounts[3] === totalBottles) {
      return 3 // 已提交：所有瓶组都已完成
    } else if (statusCounts[0] === totalBottles) {
      return 0 // 待采集：所有瓶组都是采样状态
    } else if (statusCounts[0] === 0) {
      return 2 // 已采集：所有瓶组都已完成采样（装箱或以上）
    } else {
      return 1 // 采集中：部分瓶组已开始流转
    }
  },

  // 获取样品状态文本（基于瓶组状态计算）
  getSampleStatusText(status) {
    const statusMap = {
      0: '待采集',    // 所有瓶组都是采样状态
      1: '采集中',    // 部分瓶组已开始流转
      2: '已采集',    // 所有瓶组都已完成采样（装箱或以上）
      3: '已提交'     // 所有瓶组都已完成流转
    }
    return statusMap[status] || '未知'
  },

  // Tab切换
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab

    // 检查任务状态，只有执行中或已完成的任务才能访问样品管理和点位信息
    if ((tab === 'samples' || tab === 'point') && this.data.task.status < 1) {
      wx.showModal({
        title: '提示',
        content: '请先开始执行任务，才能进行采样管理操作',
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }

    this.setData({ activeTab: tab })

    // 如果切换到样品管理Tab，确保统计数据是最新的
    if (tab === 'samples') {
      if (this.data.sampleList.length === 0) {
        // 如果没有样品数据，加载样品数据
        this.loadSampleData()
      } else {
        // 如果已有样品数据，重新计算统计
        const statistics = this.calculateSampleStatistics(this.data.sampleList)
        this.setData({ sampleStatistics: statistics })
      }
    } else if (tab === 'point') {
      // 如果切换到点位信息Tab，加载点位信息
      this.loadPointInfo()
      this.initDateTimePicker()
    }
  },

  // 加载样品数据和统计
  async loadSampleData() {
    const groupId = this.data.taskId
    if (!groupId) return

    try {
      // 加载样品记录
      const sampleList = await this.loadSampleList(groupId)

      // 为每个样品加载瓶组信息以计算正确的状态
      const samplesWithBottles = await Promise.all(
        sampleList.map(async (sample) => {
          try {
            // 加载样品的瓶组信息
            const result = await app.request({
              url: `/sampling/bottle-groups/sample/${sample.id}`,
              method: 'GET'
            })

            const bottleGroups = result.code === 200 ? (result.data || []) : []

            // 根据瓶组状态计算样品状态
            const calculatedStatus = this.calculateSampleStatusFromBottles(bottleGroups)

            return Object.assign({}, sample, {
              status: calculatedStatus, // 使用计算出的状态
              statusText: this.getSampleStatusText(calculatedStatus),
              bottleGroups: bottleGroups,
              bottleGroupsLoaded: true,
              bottleGroupsLoading: false
            })
          } catch (error) {
            console.warn(`加载样品 ${sample.id} 的瓶组信息失败:`, error)
            // 如果加载瓶组失败，使用原始状态
            return Object.assign({}, sample, {
              statusText: this.getSampleStatusText(sample.status),
              bottleGroups: [],
              bottleGroupsLoaded: false,
              bottleGroupsLoading: false
            })
          }
        })
      )

      // 计算统计数据
      const statistics = this.calculateSampleStatistics(samplesWithBottles)

      this.setData({
        sampleList: samplesWithBottles,
        sampleStatistics: statistics
      })
    } catch (error) {
      console.error('加载样品数据失败:', error)
      app.showToast('加载样品数据失败')
    }
  },

  // 计算样品统计（基于瓶组状态）
  calculateSampleStatistics(sampleList) {
    const stats = {
      totalCount: sampleList.length,
      pendingCount: 0,    // 待采集
      collectingCount: 0, // 采集中
      collectedCount: 0,  // 已采集
      submittedCount: 0   // 已提交
    }

    sampleList.forEach(sample => {
      // 根据瓶组状态计算样品状态
      const calculatedStatus = this.calculateSampleStatusFromBottles(sample.bottleGroups)

      switch (calculatedStatus) {
        case 0:
          stats.pendingCount++
          break
        case 1:
          stats.collectingCount++
          break
        case 2:
          stats.collectedCount++
          break
        case 3:
          stats.submittedCount++
          break
      }
    })

    return stats
  },

  // 移除样品采集方法，样品状态由瓶组状态自动计算

  // 加载瓶组信息
  async loadBottleGroups(e) {
    const sample = e.currentTarget.dataset.sample

    try {
      // 更新加载状态
      const sampleList = this.data.sampleList.map(item => {
        if (item.id === sample.id) {
          return Object.assign({}, item, { bottleGroupsLoading: true })
        }
        return item
      })
      this.setData({ sampleList })

      const result = await app.request({
        url: `/sampling/bottle-groups/sample/${sample.id}`, // 使用正确的接口路径
        method: 'GET'
      })

      if (result.code === 200) {
        const bottleGroups = result.data || []
        console.log(`样品 ${sample.id} 的瓶组信息:`, bottleGroups)

        // 更新样品的瓶组信息
        const updatedSampleList = this.data.sampleList.map(item => {
          if (item.id === sample.id) {
            return Object.assign({}, item, {
              bottleGroups,
              bottleGroupsLoaded: true,
              bottleGroupsLoading: false
            })
          }
          return item
        })

        this.setData({ sampleList: updatedSampleList })
        console.log('瓶组信息已更新到样品列表')
      } else {
        console.warn('获取瓶组信息失败:', result.message)
        app.showToast(result.message || '获取瓶组信息失败')
      }
    } catch (error) {
      console.error('加载瓶组信息失败:', error)
      app.showToast('加载瓶组信息失败')

      // 重置加载状态
      const sampleList = this.data.sampleList.map(item => {
        if (item.id === sample.id) {
          return Object.assign({}, item, { bottleGroupsLoading: false })
        }
        return item
      })
      this.setData({ sampleList })
    }
  },

  // 瓶组状态更新
  async handleBottleStatusUpdate(e) {
    const bottle = e.currentTarget.dataset.bottle
    const newStatus = e.currentTarget.dataset.status

    if (!bottle || newStatus === undefined) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    const statusNames = {0: '采样', 1: '装箱', 2: '流转', 3: '完成'}
    const currentStatusName = statusNames[bottle.status]
    const newStatusName = statusNames[newStatus]

    try {
      // 显示确认对话框
      const res = await new Promise((resolve, reject) => {
        wx.showModal({
          title: '状态更新确认',
          content: `确认将瓶组 ${bottle.bottleGroupCode} 的状态从"${currentStatusName}"更新为"${newStatusName}"吗？`,
          success: resolve,
          fail: reject
        })
      })

      if (!res.confirm) {
        return
      }

      // 显示加载提示
      wx.showLoading({
        title: '更新中...'
      })

      // 调用API更新状态
      const result = await app.request({
        url: `/sampling/bottle-groups/${bottle.id}/status`,
        method: 'PUT',
        data: newStatus
      })

      if (result.code === 200) {
        wx.showToast({
          title: `状态已更新为"${newStatusName}"`,
          icon: 'success'
        })

        // 更新本地数据并重新计算样品状态
        const sampleList = this.data.sampleList.map(sample => {
          if (sample.bottleGroups) {
            // 更新瓶组状态
            const updatedBottleGroups = sample.bottleGroups.map(b => {
              if (b.id === bottle.id) {
                return Object.assign({}, b, { status: newStatus })
              }
              return b
            })

            // 重新计算样品状态
            const calculatedStatus = this.calculateSampleStatusFromBottles(updatedBottleGroups)

            return Object.assign({}, sample, {
              bottleGroups: updatedBottleGroups,
              status: calculatedStatus,
              statusText: this.getSampleStatusText(calculatedStatus)
            })
          }
          return sample
        })

        // 重新计算统计数据
        const statistics = this.calculateSampleStatistics(sampleList)

        this.setData({
          sampleList: sampleList,
          sampleStatistics: statistics
        })
      } else {
        wx.showToast({
          title: result.message || '更新失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('更新瓶组状态失败:', error)
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 瓶组打印
  async handleBottlePrint(e) {
    console.log('瓶组打印方法被调用')
    const bottle = e.currentTarget.dataset.bottle
    console.log('瓶组数据:', bottle)

    if (!bottle) {
      wx.showToast({
        title: '瓶组信息不存在',
        icon: 'none'
      })
      return
    }

    // 准备打印数据
    const taskInfo = this.data.task  // 修复：使用正确的字段名
    const bottleGroup = Object.assign({}, bottle, {
      sortOrder: this.data.sampleList.findIndex(s => s.bottleGroups && s.bottleGroups.some(b => b.id === bottle.id)) + 1,
      totalCount: this.data.sampleList.length
    })

    console.log('打印数据准备:', { taskInfo, bottleGroup })

    // 跳转到打印预览页面
    const printData = {
      taskInfo: taskInfo,
      bottleGroup: bottleGroup
    }

    wx.navigateTo({
      url: `/pages/printer/preview?data=${encodeURIComponent(JSON.stringify(printData))}`
    })
  },

  // 原来的蓝牙打印方法（保留作为备用）
  async bluetoothPrintBackup(bottle) {
    try {
      // 找到当前瓶组所属的样品记录
      let sampleIndex = 0
      let totalSamples = this.data.sampleList.length

      // 遍历样品记录，找到包含当前瓶组的样品
      for (let i = 0; i < this.data.sampleList.length; i++) {
        const sample = this.data.sampleList[i]
        if (sample.bottleGroups && sample.bottleGroups.some(b => b.id === bottle.id)) {
          sampleIndex = i + 1 // 样品序号从1开始
          break
        }
      }

      // 构建样品编号：任务编号（样品序号/样品总数量）
      const taskCode = this.data.task.taskCode || this.data.task.groupCode || 'TASK-001'
      const sampleNumber = `${taskCode}（${sampleIndex}/${totalSamples}）`

      // 构建采样点位：点位名称 （周期{周期数}）
      const pointName = this.data.task.pointName || '采样点位'
      const cycleNumber = this.data.task.cycleNumber || 1
      const samplingPoint = `${pointName} （周期${cycleNumber}）`

      // 格式化采样日期
      const samplingDate = this.formatDate(new Date())

      // 构建打印内容
      const printContent = this.generatePrintContent({
        sampleCategory: bottle.bottleType || '水样',
        sampleNumber: sampleNumber,
        samplingDate: samplingDate,
        samplingPoint: samplingPoint,
        testItems: bottle.detectionMethod || '常规检测',
        container: bottle.bottleVolume || '500ml塑料瓶',
        storageMethod: '常温保存',
        bottleCode: bottle.bottleGroupCode || `瓶组${bottle.id}`,
        qrCodeText: `${sampleNumber}-${bottle.id}`
      })

      // 调用微信小程序打印接口
      console.log('准备调用打印接口，打印内容:', printContent)
      await this.printLabel(printContent)

    } catch (error) {
      console.error('打印失败:', error)
      app.showToast('打印失败: ' + error.message)
    }
  },

  // 生成打印内容 - 优化布局和间距
  generatePrintContent(data) {
    return {
      // 标签尺寸 (mm)
      width: 75,
      height: 50, // 减小高度
      // 打印内容 - 优化布局
      content: [
        {
          type: 'text',
          text: '样品类别：' + data.sampleCategory,
          x: 2,
          y: 4,
          fontSize: 9,
          fontWeight: 'bold'
        },
        {
          type: 'text',
          text: '样品编号：' + data.sampleNumber,
          x: 2,
          y: 9,
          fontSize: 8
        },
        {
          type: 'text',
          text: '采样日期：' + data.samplingDate,
          x: 2,
          y: 14,
          fontSize: 8
        },
        {
          type: 'text',
          text: '采样点位：' + data.samplingPoint,
          x: 2,
          y: 19,
          fontSize: 8
        },
        {
          type: 'text',
          text: '检测项目：' + data.testItems,
          x: 2,
          y: 24,
          fontSize: 8
        },
        {
          type: 'text',
          text: '保存容器：' + data.container,
          x: 2,
          y: 29,
          fontSize: 8
        },
        {
          type: 'text',
          text: '保存方式：' + data.storageMethod,
          x: 2,
          y: 34,
          fontSize: 8
        },
        {
          type: 'text',
          text: '样品状态：☐待测 ☐合格 ☐不合格',
          x: 2,
          y: 39,
          fontSize: 7
        },
        {
          type: 'qrcode',
          text: data.qrCodeText,
          x: 50, // 向左移动，确保在标签内
          y: 4,
          size: 16 // 减小尺寸
        }
      ]
    }
  },

  // 调用微信小程序打印接口
  async printLabel(printContent) {
    console.log('printLabel方法被调用，内容:', printContent)

    // 直接进行蓝牙打印
    try {
      await this.bluetoothPrint(printContent)
    } catch (error) {
      console.error('蓝牙打印失败:', error)
      // 降级到预览模式
      wx.showModal({
        title: '打印失败',
        content: '蓝牙打印失败，是否查看打印预览？',
        confirmText: '查看预览',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.showPrintPreview(printContent)
          }
        }
      })
    }
  },

  // 蓝牙打印
  async bluetoothPrint(printContent) {
    try {
      // 获取已配置的打印机
      const connectedPrinter = wx.getStorageSync('connectedDevice')
      const printerSettings = wx.getStorageSync('printerSettings') || {
        paperWidth: 75,
        paperHeight: 60,
        printDensity: 'medium',
        printSpeed: 'normal',
        commandSet: 'CPCL'
      }

      if (!connectedPrinter) {
        throw new Error('未找到已配置的打印机')
      }

      // 1. 初始化蓝牙适配器
      await wx.openBluetoothAdapter()

      // 2. 直接连接已配置的打印机
      wx.showLoading({ title: '连接打印机...' })

      try {
        await wx.createBLEConnection({
          deviceId: connectedPrinter.deviceId
        })

        wx.hideLoading()
        wx.showLoading({ title: '正在打印...' })

        // 3. 发送打印数据
        await this.sendPrintDataToPrinter(connectedPrinter, printContent, printerSettings)

        wx.hideLoading()
        wx.showToast({
          title: '打印完成',
          icon: 'success'
        })

      } catch (connectError) {
        wx.hideLoading()
        console.error('连接打印机失败:', connectError)

        // 连接失败，提示用户重新配置
        wx.showModal({
          title: '连接失败',
          content: '无法连接到已配置的打印机，是否重新配置？',
          confirmText: '重新配置',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/printer/config'
              })
            }
          }
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('蓝牙打印失败:', error)

      // 降级到预览模式
      this.showPrintPreview(printContent)
    }
  },

  // 发送打印数据到打印机
  async sendPrintDataToPrinter(printer, content, settings) {
    try {
      // 检查打印机是否有服务信息
      if (!printer.serviceId || !printer.characteristicId) {
        throw new Error('打印机服务信息不完整，请重新配置')
      }

      console.log('打印机信息:', printer)
      console.log('打印内容:', content)
      console.log('打印设置:', settings)

      // 生成CPCL指令
      const printCommands = this.generateCPCLCommands(content, settings)
      console.log('生成的CPCL指令:', printCommands)

      // 发送打印指令
      await this.sendBLEData(printer, printCommands)

    } catch (error) {
      console.error('发送打印数据失败:', error)
      throw error
    }
  },

  // 生成CPCL打印指令
  generateCPCLCommands(content, settings) {
    const commands = []

    // CPCL标签开始 - 使用200DPI
    const labelHeight = Math.min(settings.paperHeight * 6, 400)
    const labelStart = `! 0 200 200 ${labelHeight} 1\r\n`
    this.pushArray(commands, this.stringToBytes(labelStart))

    // 添加GB2312编码声明
    this.pushArray(commands, this.stringToBytes('ENCODING GB2312\r\n'))

    // 分离文本和二维码内容
    const textItems = content.content.filter(item => item.type === 'text')
    const qrItems = content.content.filter(item => item.type === 'qrcode')

    // 先处理所有文本内容
    textItems.forEach((item) => {
      // 使用字体3（支持中文且大小合适）
      const textX = (item.x || 2) * 4
      const textY = (item.y || 4) * 4
      const textCmd = `TEXT 3 0 ${textX} ${textY} `
      this.pushArray(commands, this.stringToBytes(textCmd))

      // 使用GBK编码处理中文内容
      const gbkEncoder = new TextEncoder('gbk', { NONSTANDARD_allowLegacyEncoding: true })
      const textBytes = Array.from(gbkEncoder.encode(item.text))
      this.pushArray(commands, textBytes)
      this.pushArray(commands, this.stringToBytes('\r\n'))
    })

    // 再处理二维码 - 使用绝对坐标
    qrItems.forEach((item) => {
      const qrX = (item.x || 50) * 4  // 使用绝对坐标
      const qrY = (item.y || 4) * 4   // 使用绝对坐标
      const qrCmd = `BARCODE QR ${qrX} ${qrY} M 2 U 4\r\n`
      this.pushArray(commands, this.stringToBytes(qrCmd))
      this.pushArray(commands, this.stringToBytes(`MA,${item.text}\r\n`))
      this.pushArray(commands, this.stringToBytes('ENDQR\r\n'))
    })

    // 打印标签
    this.pushArray(commands, this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 字符串转字节数组（ASCII）
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i))
    }
    return bytes
  },

  // 字符串转GBK编码（简化版）
  stringToGBK(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i)
      const code = str.charCodeAt(i)

      if (code < 0x80) {
        // ASCII字符直接输出
        bytes.push(code)
      } else {
        // 中文字符处理
        // 对于CPCL打印机，尝试使用简化的中文处理
        // 这里使用一个简化的映射，实际应该使用完整的GBK码表
        const gbkBytes = this.chineseToGBK(char)
        if (gbkBytes.length > 0) {
          this.pushArray(bytes, gbkBytes)
        } else {
          // 如果无法转换，使用问号替代
          bytes.push(0x3F) // '?'
        }
      }
    }
    return bytes
  },

  // 简化的中文字符转GBK
  chineseToGBK(char) {
    // 常用中文字符的GBK编码映射（简化版）
    const commonChars = {
      '样': [0xD1, 0xF9],
      '品': [0xC6, 0xB7],
      '类': [0xC0, 0xE0],
      '别': [0xB1, 0xF0],
      '编': [0xB1, 0xE0],
      '号': [0xBA, 0xC5],
      '采': [0xB2, 0xC9],
      '日': [0xC8, 0xD5],
      '期': [0xC6, 0xDA],
      '点': [0xB5, 0xE3],
      '位': [0xCE, 0xBB],
      '检': [0xBC, 0xEC],
      '测': [0xB2, 0xE2],
      '项': [0xCF, 0xEE],
      '目': [0xC4, 0xBF],
      '保': [0xB1, 0xA3],
      '存': [0xB4, 0xE6],
      '容': [0xC8, 0xDD],
      '器': [0xC6, 0xF7],
      '方': [0xB7, 0xBD],
      '式': [0xCA, 0xBD],
      '状': [0xD7, 0xB4],
      '态': [0xCC, 0xAC],
      '待': [0xB4, 0xFD],
      '合': [0xBA, 0xCF],
      '格': [0xB8, 0xF1],
      '不': [0xB2, 0xBB],
      '水': [0xCB, 0xAE],
      '常': [0xB3, 0xA3],
      '规': [0xB9, 0xE6],
      '温': [0xCE, 0xC2],
      '瓶': [0xC6, 0xBF],
      '组': [0xD7, 0xE9],
      '周': [0xD6, 0xDC],
      '期': [0xC6, 0xDA]
    }

    if (commonChars[char]) {
      return commonChars[char]
    }

    // 如果不在常用字符中，尝试使用Unicode转换（简化处理）
    const code = char.charCodeAt(0)
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 中文字符范围，使用简化的双字节编码
      const high = Math.floor((code - 0x4E00) / 256) + 0xA1
      const low = ((code - 0x4E00) % 256) + 0xA1
      return [Math.min(high, 0xFE), Math.min(low, 0xFE)]
    }

    return [] // 无法转换
  },

  // 字符串转UTF-8编码
  stringToUTF8(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i)
      if (code < 0x80) {
        bytes.push(code)
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6))
        bytes.push(0x80 | (code & 0x3F))
      } else {
        bytes.push(0xE0 | (code >> 12))
        bytes.push(0x80 | ((code >> 6) & 0x3F))
        bytes.push(0x80 | (code & 0x3F))
      }
    }
    return bytes
  },

  // 发送BLE数据
  async sendBLEData(device, data) {
    const maxChunkSize = 20 // BLE每次最大传输20字节

    console.log('开始发送数据，总长度:', data.length, '字节')

    for (let i = 0; i < data.length; i += maxChunkSize) {
      const chunk = data.slice(i, i + maxChunkSize)

      await wx.writeBLECharacteristicValue({
        deviceId: device.deviceId,
        serviceId: device.serviceId,
        characteristicId: device.characteristicId,
        value: chunk.buffer
      })

      // 每次发送后稍作延迟，避免数据丢失
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    console.log('数据发送完成')
  },

  // 原有的搜索打印机方法（保留作为备用）
  async searchAndConnectPrinter(printContent) {
    try {
      // 2. 搜索蓝牙设备
      wx.showLoading({ title: '搜索打印机...' })

      await wx.startBluetoothDevicesDiscovery({
        services: [],
        allowDuplicatesKey: false
      })

      // 3. 获取已发现的设备
      setTimeout(async () => {
        try {
          const devices = await wx.getBluetoothDevices()
          wx.hideLoading()

          if (devices.devices.length === 0) {
            throw new Error('未找到蓝牙设备')
          }

          // 4. 显示设备选择列表
          this.showPrinterSelection(devices.devices, printContent)

        } catch (error) {
          wx.hideLoading()
          throw error
        }
      }, 3000)

    } catch (error) {
      wx.hideLoading()
      console.error('蓝牙打印失败:', error)

      if (error.errCode === 10001) {
        wx.showModal({
          title: '提示',
          content: '请先开启蓝牙功能',
          showCancel: false
        })
      } else {
        // 降级到预览模式
        this.showPrintPreview(printContent)
      }
    }
  },

  // 显示打印机选择
  showPrinterSelection(devices, printContent) {
    const printerDevices = devices.filter(device =>
      device.name && (
        device.name.toLowerCase().includes('printer') ||
        device.name.toLowerCase().includes('print') ||
        device.name.includes('打印')
      )
    )

    if (printerDevices.length === 0) {
      wx.showModal({
        title: '未找到打印机',
        content: '未发现可用的蓝牙打印机，是否查看打印预览？',
        success: (res) => {
          if (res.confirm) {
            this.showPrintPreview(printContent)
          }
        }
      })
      return
    }

    const deviceNames = printerDevices.map(device => device.name || '未知设备')

    wx.showActionSheet({
      itemList: deviceNames,
      success: (res) => {
        const selectedDevice = printerDevices[res.tapIndex]
        this.connectAndPrint(selectedDevice, printContent)
      }
    })
  },

  // 连接设备并打印
  async connectAndPrint(device, printContent) {
    try {
      wx.showLoading({ title: '连接打印机...' })

      // 连接设备
      await wx.createBLEConnection({
        deviceId: device.deviceId
      })

      // 获取服务
      const services = await wx.getBLEDeviceServices({
        deviceId: device.deviceId
      })

      // 这里需要根据具体打印机的服务和特征值进行配置
      // 由于不同打印机的协议不同，这里提供一个通用的框架

      wx.hideLoading()
      app.showToast('打印机连接成功')

      // 发送打印数据
      await this.sendPrintData(device.deviceId, printContent)

    } catch (error) {
      wx.hideLoading()
      console.error('连接打印机失败:', error)
      app.showToast('连接打印机失败')
    }
  },

  // 发送打印数据
  async sendPrintData(deviceId, printContent) {
    try {
      // 这里需要根据具体打印机协议转换打印内容
      // 不同品牌的打印机有不同的指令集

      // 示例：ESC/POS指令集
      const printCommands = this.generateESCPOSCommands(printContent)

      // 发送数据到打印机
      // 这里需要找到正确的服务UUID和特征值UUID

      app.showToast('打印任务已发送', 'success')

    } catch (error) {
      console.error('发送打印数据失败:', error)
      app.showToast('打印失败')
    }
  },

  // 生成ESC/POS打印指令
  generateESCPOSCommands(printContent) {
    // 这里需要根据ESC/POS协议生成打印指令
    // 由于比较复杂，这里只是一个示例框架
    const commands = []

    // 初始化打印机
    commands.push([0x1B, 0x40])

    // 设置字体大小
    commands.push([0x1B, 0x21, 0x00])

    // 打印文本内容
    printContent.content.forEach(item => {
      if (item.type === 'text') {
        const textBytes = this.stringToBytes(item.text + '\n')
        commands.push(textBytes)
      }
    })

    // 切纸
    commands.push([0x1D, 0x56, 0x00])

    return commands
  },

  // 字符串转字节数组
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i))
    }
    return bytes
  },

  // 显示打印预览（降级方案）
  showPrintPreview(printContent) {
    wx.showModal({
      title: '打印预览',
      content: '由于未找到可用打印机，将显示打印预览。您可以截图保存或分享。',
      confirmText: '查看预览',
      success: (res) => {
        if (res.confirm) {
          // 跳转到打印预览页面
          wx.navigateTo({
            url: `/pages/sampling/print-preview?data=${encodeURIComponent(JSON.stringify(printContent))}`
          })
        }
      }
    })
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },





  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  },

  // 开始任务
  async startTask() {
    try {
      // 显示确认对话框
      const res = await new Promise((resolve, reject) => {
        wx.showModal({
          title: '确认开始执行',
          content: '确认开始执行该任务吗？开始后将可以进行采样管理操作。',
          success: resolve,
          fail: reject
        })
      })

      if (!res.confirm) {
        return
      }

      app.showLoading('启动中...')

      // 使用分组状态更新接口
      await app.request({
        url: `/sampling/task-group/${this.data.taskId}/status`,
        method: 'PUT',
        data: 1 // 1表示执行中状态
      })

      app.showToast('任务已开始执行', 'success')

      // 重新加载任务详情
      await this.loadTaskDetail(this.data.taskId)

      // 如果当前在样品管理或点位信息Tab，且任务状态变为执行中，保持当前Tab
      if ((this.data.activeTab === 'samples' || this.data.activeTab === 'point') && this.data.task.status >= 1) {
        // 保持当前Tab
      } else if (this.data.task.status >= 1) {
        // 任务开始后，自动切换到样品管理Tab
        this.setData({ activeTab: 'samples' })
      }

    } catch (error) {
      console.error('开始任务失败:', error)
      app.showToast('操作失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 完成任务
  async completeTask() {
    // 检查是否所有样品都已完成（状态为3表示已提交）
    const pendingSamples = this.data.sampleList.filter(sample =>
      sample.status !== 3
    )

    if (pendingSamples.length > 0) {
      wx.showModal({
        title: '提示',
        content: `还有${pendingSamples.length}个样品未完成，确定要完成任务吗？`,
        success: (res) => {
          if (res.confirm) {
            this.performCompleteTask()
          }
        }
      })
    } else {
      this.performCompleteTask()
    }
  },

  // 执行完成任务
  async performCompleteTask() {
    try {
      app.showLoading('提交中...')

      // 使用分组状态更新接口
      await app.request({
        url: `/sampling/task-group/${this.data.taskId}/status`,
        method: 'PUT',
        data: 2 // 2表示已完成状态
      })

      app.showToast('任务已完成', 'success')
      this.loadTaskDetail(this.data.taskId)

    } catch (error) {
      console.error('完成任务失败:', error)
      app.showToast('操作失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 采集样品
  async collectSample(e) {
    const sampleId = e.currentTarget.dataset.id
    
    try {
      await app.request({
        url: `/api/sampling/samples/${sampleId}/collect`,
        method: 'POST'
      })
      
      app.showToast('样品已采集', 'success')
      this.loadTaskDetail(this.data.taskId)
      
    } catch (error) {
      console.error('采集样品失败:', error)
      app.showToast('操作失败，请重试')
    }
  },

  // 提交样品
  async submitSample(e) {
    const sampleId = e.currentTarget.dataset.id
    
    try {
      await app.request({
        url: `/api/sampling/samples/${sampleId}/submit`,
        method: 'POST'
      })
      
      app.showToast('样品已提交', 'success')
      this.loadTaskDetail(this.data.taskId)
      
    } catch (error) {
      console.error('提交样品失败:', error)
      app.showToast('操作失败，请重试')
    }
  },

  // 查看样品详情
  viewSampleDetail(e) {
    const sampleId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/sampling/sample-detail?id=${sampleId}`
    })
  },

  // 添加样品
  addSample() {
    wx.navigateTo({
      url: `/pages/sampling/add-sample?taskId=${this.data.taskId}`
    })
  },

  // 扫码操作
  scanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        app.showToast('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult(result) {
    try {
      const data = JSON.parse(result)
      
      if (data.type === 'sample') {
        // 样品二维码
        wx.navigateTo({
          url: `/pages/sampling/sample-detail?id=${data.id}`
        })
      } else if (data.type === 'equipment') {
        // 设备二维码
        wx.navigateTo({
          url: `/pages/equipment/detail?id=${data.id}`
        })
      } else {
        app.showToast('未识别的二维码类型')
      }
    } catch (error) {
      app.showToast(`扫码结果: ${result}`)
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `任务详情 - ${this.data.task.title}`,
      path: `/pages/sampling/task-detail?id=${this.data.taskId}`,
      imageUrl: '/static/images/task-share.png'
    }
  },

  // ==================== 点位信息相关方法 ====================

  // 加载点位信息
  async loadPointInfo() {
    const groupId = this.data.taskId
    if (!groupId) return

    this.setData({ pointInfoLoading: true })

    try {
      const result = await app.request({
        url: `/sampling/point-info/group/${groupId}`,
        method: 'GET'
      })

      if (result.code === 200 && result.data) {
        const pointInfo = result.data

        // 更新选择器索引
        const weatherIndex = this.data.weatherOptions.indexOf(pointInfo.weatherCondition || '')
        const windDirectionIndex = this.data.windDirectionOptions.indexOf(pointInfo.windDirection || '')

        this.setData({
          pointInfo: Object.assign({}, this.data.pointInfo, pointInfo),
          weatherIndex: weatherIndex >= 0 ? weatherIndex : -1,
          windDirectionIndex: windDirectionIndex >= 0 ? windDirectionIndex : -1
        })

        // 如果有采样时间，更新时间选择器
        if (pointInfo.samplingTime) {
          this.updateDateTimePickerFromString(pointInfo.samplingTime)
        }
      } else {
        console.log('该分组暂无点位信息，可以创建新的点位信息')
      }
    } catch (error) {
      console.error('加载点位信息失败:', error)
      app.showToast('加载点位信息失败')
    } finally {
      this.setData({ pointInfoLoading: false })
    }
  },

  // 保存点位信息
  async savePointInfo() {
    const groupId = this.data.taskId
    if (!groupId) return

    try {
      // 准备数据，确保数字字段的类型正确
      const rawData = Object.assign({}, this.data.pointInfo, {
        samplingTaskGroupId: groupId
      })

      // 转换数字字段
      const data = this.formatPointInfoData(rawData)

      let result
      if (this.data.pointInfo.id) {
        // 更新现有点位信息
        result = await app.request({
          url: `/sampling/point-info/update/${this.data.pointInfo.id}`,
          method: 'PUT',
          data: data
        })
      } else {
        // 创建新的点位信息
        result = await app.request({
          url: '/sampling/point-info/create',
          method: 'POST',
          data: data
        })
      }

      if (result.code === 200) {
        if (result.data && result.data.id && !this.data.pointInfo.id) {
          // 如果是新创建的，保存ID
          this.setData({
            'pointInfo.id': result.data.id
          })
        }
        console.log('点位信息保存成功')
      } else {
        console.error('保存点位信息失败:', result.msg || result.message)
        app.showToast(result.msg || result.message || '保存失败')
      }
    } catch (error) {
      console.error('保存点位信息异常:', error)
      app.showToast('保存失败')
    }
  },

  // 格式化点位信息数据，确保数字字段类型正确
  formatPointInfoData(rawData) {
    const data = Object.assign({}, rawData)

    // 转换数字字段
    if (data.longitude) {
      data.longitude = parseFloat(data.longitude) || null
    }
    if (data.latitude) {
      data.latitude = parseFloat(data.latitude) || null
    }
    if (data.altitude) {
      data.altitude = parseFloat(data.altitude) || null
    }
    if (data.temperature) {
      data.temperature = parseFloat(data.temperature) || null
    }
    if (data.humidity) {
      data.humidity = parseFloat(data.humidity) || null
    }
    if (data.windSpeed) {
      data.windSpeed = parseFloat(data.windSpeed) || null
    }

    // 移除空字符串字段
    Object.keys(data).forEach(key => {
      if (data[key] === '') {
        data[key] = null
      }
    })

    return data
  },

  // 初始化时间选择器
  initDateTimePicker() {
    const now = new Date()
    const currentYear = now.getFullYear()

    // 生成年份范围（当前年份前后5年）
    const years = []
    for (let i = currentYear - 5; i <= currentYear + 5; i++) {
      years.push(i + '年')
    }

    // 生成月份
    const months = []
    for (let i = 1; i <= 12; i++) {
      months.push(i + '月')
    }

    // 生成日期
    const days = []
    for (let i = 1; i <= 31; i++) {
      days.push(i + '日')
    }

    // 生成时分
    const hours = []
    for (let i = 0; i < 24; i++) {
      hours.push(String(i).padStart(2, '0') + ':00')
    }

    this.setData({
      dateTimeRange: [years, months, days, hours],
      dateTimeValue: [5, now.getMonth(), now.getDate() - 1, now.getHours()]
    })
  },

  // 从字符串更新时间选择器
  updateDateTimePickerFromString(timeString) {
    try {
      const date = new Date(timeString)
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hour = date.getHours()

      // 找到对应的索引
      const yearIndex = this.data.dateTimeRange[0].findIndex(item => item === year + '年')
      const monthIndex = month - 1
      const dayIndex = day - 1
      const hourIndex = hour

      if (yearIndex >= 0) {
        this.setData({
          dateTimeValue: [yearIndex, monthIndex, dayIndex, hourIndex]
        })
      }
    } catch (error) {
      console.error('解析时间字符串失败:', error)
    }
  },

  // 输入事件处理

  onLongitudeInput(e) {
    this.setData({
      'pointInfo.longitude': e.detail.value
    })
  },

  onLatitudeInput(e) {
    this.setData({
      'pointInfo.latitude': e.detail.value
    })
  },

  onAltitudeInput(e) {
    this.setData({
      'pointInfo.altitude': e.detail.value
    })
  },

  onTemperatureInput(e) {
    this.setData({
      'pointInfo.temperature': e.detail.value
    })
  },

  onHumidityInput(e) {
    this.setData({
      'pointInfo.humidity': e.detail.value
    })
  },

  onWindSpeedInput(e) {
    this.setData({
      'pointInfo.windSpeed': e.detail.value
    })
  },

  onRemarksInput(e) {
    this.setData({
      'pointInfo.remarks': e.detail.value
    })
  },

  // 选择器事件处理
  onSamplingTimeChange(e) {
    const values = e.detail.value
    const ranges = this.data.dateTimeRange

    if (ranges.length >= 4 && values.length >= 4) {
      const year = parseInt(ranges[0][values[0]])
      const month = parseInt(ranges[1][values[1]])
      const day = parseInt(ranges[2][values[2]])
      const hourStr = ranges[3][values[3]]
      const hour = parseInt(hourStr.split(':')[0])

      const date = new Date(year, month - 1, day, hour, 0, 0)
      const timeString = date.toISOString().slice(0, 19).replace('T', ' ')

      this.setData({
        'pointInfo.samplingTime': timeString,
        dateTimeValue: values
      })

      this.savePointInfo()
    }
  },

  onWeatherChange(e) {
    const index = e.detail.value
    const weather = this.data.weatherOptions[index]

    this.setData({
      'pointInfo.weatherCondition': weather,
      weatherIndex: index
    })

    this.savePointInfo()
  },

  onWindDirectionChange(e) {
    const index = e.detail.value
    const direction = this.data.windDirectionOptions[index]

    this.setData({
      'pointInfo.windDirection': direction,
      windDirectionIndex: index
    })

    this.savePointInfo()
  },

  // 获取当前位置
  getCurrentLocation() {
    if (this.data.locationLoading) return

    this.setData({ locationLoading: true })

    wx.showToast({
      title: '正在获取位置...',
      icon: 'loading',
      duration: 2000
    })

    wx.getLocation({
      type: 'gcj02',
      altitude: true,
      success: (res) => {
        console.log('获取位置成功:', res)

        this.setData({
          'pointInfo.longitude': res.longitude.toFixed(6),
          'pointInfo.latitude': res.latitude.toFixed(6),
          'pointInfo.altitude': res.altitude ? Math.round(res.altitude) : '',
          locationLoading: false
        })

        wx.showToast({
          title: '位置获取成功',
          icon: 'success'
        })

        this.savePointInfo()
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        this.setData({ locationLoading: false })

        let errorMsg = '位置获取失败'
        if (error.errMsg.includes('auth deny')) {
          errorMsg = '位置权限被拒绝，请在设置中允许位置访问'
        } else if (error.errMsg.includes('location fail')) {
          errorMsg = '定位服务不可用，请检查设备定位设置'
        }

        wx.showModal({
          title: '位置获取失败',
          content: errorMsg + '，您可以手动输入坐标信息',
          showCancel: false
        })
      }
    })
  },

  // 照片上传相关方法
  uploadPointPhoto(e) {
    const type = e.currentTarget.dataset.type
    this.chooseAndUploadImage('pointPhotoUrl', '点位照片')
  },

  uploadEnvironmentPhoto(e) {
    const direction = e.currentTarget.dataset.direction
    const fieldName = `${direction}PhotoUrl`
    const displayName = `${direction === 'east' ? '东' : direction === 'south' ? '南' : direction === 'west' ? '西' : '北'}侧照片`

    this.chooseAndUploadImage(fieldName, displayName)
  },

  // 选择并上传图片
  chooseAndUploadImage(fieldName, displayName) {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        this.uploadImageToServer(tempFilePath, fieldName, displayName)
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        app.showToast('选择图片失败')
      }
    })
  },

  // 上传图片到服务器
  async uploadImageToServer(filePath, fieldName, displayName) {
    wx.showLoading({
      title: `上传${displayName}中...`
    })

    try {
      const uploadResult = await new Promise((resolve, reject) => {
        wx.uploadFile({
          url: `${app.globalData.baseURL}/common/upload`,
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': `Bearer ${wx.getStorageSync('token')}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data)
              if (data.code === 200) {
                resolve(data)
              } else {
                reject(new Error(data.msg || '上传失败'))
              }
            } catch (error) {
              reject(new Error('解析上传结果失败'))
            }
          },
          fail: reject
        })
      })

      // 更新点位信息中的图片URL
      this.setData({
        [`pointInfo.${fieldName}`]: uploadResult.url
      })

      // 保存到服务器
      await this.savePointInfo()

      wx.hideLoading()
      wx.showToast({
        title: `${displayName}上传成功`,
        icon: 'success'
      })

    } catch (error) {
      wx.hideLoading()
      console.error('上传图片失败:', error)
      app.showToast(error.message || `上传${displayName}失败`)
    }
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url
    if (!url) return

    // 收集所有已上传的图片URL
    const urls = []
    const pointInfo = this.data.pointInfo

    if (pointInfo.pointPhotoUrl) {
      urls.push(this.getImageUrl(pointInfo.pointPhotoUrl))
    }
    if (pointInfo.eastPhotoUrl) {
      urls.push(this.getImageUrl(pointInfo.eastPhotoUrl))
    }
    if (pointInfo.southPhotoUrl) {
      urls.push(this.getImageUrl(pointInfo.southPhotoUrl))
    }
    if (pointInfo.westPhotoUrl) {
      urls.push(this.getImageUrl(pointInfo.westPhotoUrl))
    }
    if (pointInfo.northPhotoUrl) {
      urls.push(this.getImageUrl(pointInfo.northPhotoUrl))
    }

    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 获取完整图片URL
  getImageUrl(url) {
    if (!url) return ''
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }
    return `${app.globalData.baseURL || 'http://127.0.0.1:9099'}${url}`
  }
})
