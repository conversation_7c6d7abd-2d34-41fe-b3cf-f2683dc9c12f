/* pages/sampling/task-detail.wxss */

/* 容器样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 30rpx 20rpx 20rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.task-number {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.task-status-header {
  display: flex;
  align-items: center;
}

.status-label {
  font-size: 26rpx;
  margin-right: 10rpx;
}

.status-badge {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.2);
}

/* Tab容器 */
.tab-container {
  background: white;
}

.tabs {
  display: flex;
  border-bottom: 1rpx solid #E4E7ED;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #606266;
  position: relative;
}

.tab-item.active {
  color: #409EFF;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #409EFF;
  border-radius: 2rpx;
}

.tab-content {
  min-height: 60vh;
}

.content {
  padding: 20rpx;
}

/* 任务基本信息 */
.task-info {
  margin-bottom: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  flex: 1;
  margin-right: 20rpx;
}

.task-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.status-pending {
  background: #f0f9ff;
  color: #409EFF;
}

.status-in_progress {
  background: #f0f9f0;
  color: #67C23A;
}

.status-completed {
  background: #fdf6ec;
  color: #E6A23C;
}

.status-cancelled {
  background: #fef0f0;
  color: #F56C6C;
}

.task-code {
  font-size: 24rpx;
  color: #909399;
  margin-bottom: 20rpx;
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 30rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-size: 26rpx;
  color: #606266;
  width: 160rpx;
}

.value {
  font-size: 26rpx;
  color: #303133;
  flex: 1;
  text-align: right;
}

/* 任务进度 */
.task-progress {
  margin-top: 20rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #606266;
}

.progress-percent {
  font-size: 24rpx;
  color: #409EFF;
  font-weight: bold;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 4rpx;
  transition: width 0.3s;
}

/* 旧的样品列表样式已移除，使用Tab页面的新样式 */

/* 旧的样品操作样式已移除，使用Tab页面的新样式 */

/* 空状态 */
.empty-samples {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-samples image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-samples text {
  font-size: 26rpx;
  color: #909399;
}

/* 任务操作 */
.task-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: white;
  border-radius: 20rpx;
  position: sticky;
  bottom: 20rpx;
  margin: 20rpx 0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.start-btn {
  background: #67C23A;
  color: white;
}

.complete-btn {
  background: #409EFF;
  color: white;
}

.start-task-btn {
  background: #67C23A;
  color: white;
}

.complete-task-btn {
  background: #409EFF;
  color: white;
}

.scan-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
}

/* 任务完成状态 */
.task-completed {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background: #F0F9FF;
  border: 2rpx solid #67C23A;
  border-radius: 40rpx;
}

.completed-text {
  color: #67C23A;
  font-size: 28rpx;
  font-weight: bold;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #606266;
}

/* 样品管理样式 */
.section {
  background: white;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  background: #f8f9fa;
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  border-bottom: 1rpx solid #e9ecef;
}

/* 任务信息网格 */
.info-grid {
  padding: 30rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.info-item .label {
  font-size: 26rpx;
  color: #909399;
  min-width: 160rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.info-item .value {
  font-size: 26rpx;
  color: #303133;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

/* 样品统计 */
.statistics-grid {
  display: flex;
  background: #f8f9fa;
  margin: 0 30rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  flex: 1;
  background: white;
  padding: 40rpx 20rpx;
  text-align: center;
  position: relative;
  border-right: 2rpx solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-number {
  font-size: 64rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12rpx;
  line-height: 1;
}

.stat-label {
  font-size: 26rpx;
  color: #606266;
  line-height: 1.2;
  font-weight: 500;
}

/* 不同状态的数字颜色和背景 */
.stat-item.pending {
  background: linear-gradient(135deg, #fff7e6, #ffffff);
}

.stat-item.pending .stat-number {
  color: #fa8c16;
}

.stat-item.collecting {
  background: linear-gradient(135deg, #fff7e6, #ffffff);
}

.stat-item.collecting .stat-number {
  color: #fa8c16;
}

.stat-item.collected {
  background: linear-gradient(135deg, #e6f7ff, #ffffff);
}

.stat-item.collected .stat-number {
  color: #1890ff;
}

.stat-item.submitted {
  background: linear-gradient(135deg, #f6ffed, #ffffff);
}

.stat-item.submitted .stat-number {
  color: #52c41a;
}

/* 添加小图标效果 */
.stat-item::before {
  content: '';
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.3;
}

.stat-item.pending::before {
  background: #fa8c16;
}

.stat-item.collecting::before {
  background: #fa8c16;
}

.stat-item.collected::before {
  background: #1890ff;
}

.stat-item.submitted::before {
  background: #52c41a;
}

/* 样品列表 */
.sample-list {
  padding: 0 30rpx 30rpx;
}

.sample-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #e4e7ed;
}

.sample-item:last-child {
  margin-bottom: 0;
}

.sample-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.sample-number {
  font-size: 28rpx;
  color: #303133;
  font-weight: bold;
}

.sample-status {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.sample-status.status-0 {
  background: #e3f2fd;
  color: #1976d2;
}

.sample-status.status-1 {
  background: #fff3e0;
  color: #f57c00;
}

.sample-status.status-2 {
  background: #e6f7ff;
  color: #1890ff;
}

.sample-status.status-3 {
  background: #e8f5e8;
  color: #388e3c;
}

.sample-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

/* 样品记录中的信息行样式 */
.sample-info .info-row .label {
  font-size: 24rpx;
  color: #909399;
  min-width: 140rpx;
  flex-shrink: 0;
}

.sample-info .info-row .value {
  font-size: 24rpx;
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.sample-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.sample-action-btn {
  padding: 12rpx 24rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
  margin-left: 16rpx;
}

.sample-action-btn.primary {
  background: #409EFF;
  color: white;
}

.sample-action-btn.warning {
  background: #E6A23C;
  color: white;
}

.sample-action-btn.info {
  background: #909399;
  color: white;
}

/* 瓶组信息 */
.bottle-groups {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e4e7ed;
}

.bottle-groups-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16rpx;
}

.bottle-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.bottle-item {
  background: #f8f9fa;
  border: 1rpx solid #e4e7ed;
  border-radius: 8rpx;
  padding: 20rpx;
  border-left: 4rpx solid #409EFF;
}

.bottle-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.bottle-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #303133;
}

.bottle-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.bottle-status.status-0 {
  background: #e3f2fd;
  color: #1976d2;
}

.bottle-status.status-1 {
  background: #fff3e0;
  color: #f57c00;
}

.bottle-status.status-2 {
  background: #f3e5f5;
  color: #7b1fa2;
}

.bottle-status.status-3 {
  background: #e8f5e8;
  color: #388e3c;
}

.bottle-details {
  margin-bottom: 16rpx;
}

.bottle-detail-row {
  display: flex;
  margin-bottom: 8rpx;
  align-items: flex-start;
}

.bottle-detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 22rpx;
  color: #909399;
  min-width: 120rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 22rpx;
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.bottle-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12rpx;
}

/* 加载瓶组按钮 */
.load-bottles {
  text-align: center;
  padding: 20rpx 0;
}

.load-bottles-btn {
  background: #f0f9ff;
  color: #409EFF;
  border: 1rpx solid #409EFF;
  border-radius: 6rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #909399;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #c0c4cc;
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #909399;
  font-size: 24rpx;
  line-height: 1.6;
}

/* ==================== 点位信息样式 ==================== */

/* 点位信息加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 20rpx;
  color: #666;
}

.loading-section .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e4e7ed;
  border-top: 4rpx solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-section .loading-text {
  font-size: 28rpx;
  color: #909399;
}

/* 表单网格布局 */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 表单项 */
.form-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.form-item.full-width {
  width: 100%;
}

.form-label {
  font-size: 28rpx;
  color: #303133;
  font-weight: 500;
}

.form-input {
  padding: 24rpx;
  border: 2rpx solid #dcdfe6;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}

.form-input:focus {
  border-color: #409eff;
}

.form-picker {
  padding: 24rpx;
  border: 2rpx solid #dcdfe6;
  border-radius: 8rpx;
  background: white;
}

.picker-display {
  font-size: 28rpx;
  color: #303133;
}

.form-textarea {
  padding: 24rpx;
  border: 2rpx solid #dcdfe6;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
  min-height: 120rpx;
}

.textarea-counter {
  text-align: right;
  font-size: 24rpx;
  color: #909399;
  margin-top: 8rpx;
}

/* 位置信息相关 */
.location-btn {
  padding: 12rpx 24rpx;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-left: auto;
}

.location-btn.loading {
  background: #c0c4cc;
}

.location-tips {
  margin: 20rpx 0;
  padding: 20rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #409eff;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #606266;
  line-height: 1.4;
}

.coordinate-display {
  margin-top: 20rpx;
}

.coordinate-tag {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f0f9ff;
  border: 2rpx solid #b3d8ff;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #409eff;
}

.coordinate-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.coordinate-text {
  font-size: 24rpx;
}

/* 照片上传相关 */
.photo-upload-area {
  margin-top: 20rpx;
}

.photo-item {
  margin-bottom: 32rpx;
}

.photo-label {
  display: block;
  font-size: 28rpx;
  color: #303133;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.photo-container {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 2rpx dashed #dcdfe6;
  border-radius: 8rpx;
  color: #c0c4cc;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
}

.photo-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 8rpx;
}

.photo-action-btn {
  width: 100%;
  padding: 8rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #303133;
  border: none;
  border-radius: 4rpx;
  font-size: 22rpx;
}

/* 环境照片网格 */
.environment-photos {
  margin-top: 20rpx;
}

.photo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.photo-grid .photo-item {
  margin-bottom: 0;
}

.photo-grid .photo-container {
  width: 100%;
  height: 160rpx;
}

.photo-grid .photo-label {
  text-align: center;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .photo-grid {
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
  }

  .photo-grid .photo-container {
    height: 140rpx;
  }
}

@media (max-width: 600rpx) {
  .form-grid {
    gap: 20rpx;
  }

  .form-item {
    gap: 10rpx;
  }

  .photo-grid {
    gap: 16rpx;
  }

  .photo-grid .photo-container {
    height: 120rpx;
  }
}
