// pages/equipment/detail.js
const app = getApp()

Page({
  data: {
    equipmentId: '',
    equipment: {},
    maintenanceRecords: [],
    loading: true
  },

  onLoad(options) {
    console.log('设备详情页面加载')
    const equipmentId = options.id
    if (equipmentId) {
      this.setData({ equipmentId })
      this.loadEquipmentDetail(equipmentId)
    } else {
      this.loadMockData()
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
  },

  onPullDownRefresh() {
    this.loadEquipmentDetail(this.data.equipmentId).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载设备详情
  async loadEquipmentDetail(equipmentId) {
    this.setData({ loading: true })

    try {
      const results = await Promise.all([
        this.loadEquipmentInfo(equipmentId),
        this.loadMaintenanceRecords(equipmentId)
      ])

      const equipmentData = results[0]
      const recordsData = results[1]

      this.setData({
        equipment: equipmentData,
        maintenanceRecords: recordsData
      })

    } catch (error) {
      console.error('加载设备详情失败:', error)
      app.showToast('加载失败，请重试')
      this.loadMockData()
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载设备信息
  loadEquipmentInfo(equipmentId) {
    return app.request({
      url: `/api/equipment/${equipmentId}`,
      method: 'GET'
    }).then(data => {
      return {
        ...data,
        statusText: this.getStatusText(data.status),
        runStatusText: this.getRunStatusText(data.runStatus),
        lastCheck: this.formatDateTime(data.lastCheck)
      }
    }).catch(error => {
      console.error('加载设备信息失败:', error)
      throw error
    })
  },

  // 加载维护记录
  loadMaintenanceRecords(equipmentId) {
    return app.request({
      url: `/api/equipment/${equipmentId}/maintenance-records`,
      method: 'GET',
      data: { limit: 5 }
    }).then(data => {
      return (data.items || []).map(record => ({
        ...record,
        date: this.formatDate(record.createTime)
      }))
    }).catch(error => {
      console.error('加载维护记录失败:', error)
      return []
    })
  },

  // 加载模拟数据
  loadMockData() {
    const mockEquipment = {
      id: 1,
      name: '高效液相色谱仪',
      code: 'HPLC-001',
      model: 'Agilent 1260',
      location: '实验室A区-01',
      manager: '张三',
      status: 'online',
      statusText: '在线',
      runStatus: 'normal',
      runStatusText: '正常',
      temperature: 25,
      humidity: 60,
      usageTime: 120,
      lastCheck: '2024-01-15 10:30'
    }

    const mockRecords = [
      {
        id: 1,
        type: '日常维护',
        content: '清洁设备外观，检查管路连接',
        operator: '李四',
        date: '01-15'
      },
      {
        id: 2,
        type: '校准检查',
        content: '进行标准样品校准，结果正常',
        operator: '王五',
        date: '01-10'
      },
      {
        id: 3,
        type: '故障维修',
        content: '更换进样器密封圈',
        operator: '赵六',
        date: '01-05'
      }
    ]

    this.setData({
      equipment: mockEquipment,
      maintenanceRecords: mockRecords,
      loading: false
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'online': '在线',
      'offline': '离线',
      'maintenance': '维护中',
      'error': '故障'
    }
    return statusMap[status] || '未知'
  },

  // 获取运行状态文本
  getRunStatusText(runStatus) {
    const statusMap = {
      'normal': '正常',
      'warning': '警告',
      'error': '异常',
      'stopped': '停止'
    }
    return statusMap[runStatus] || '未知'
  },

  // 格式化日期时间
  formatDateTime(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  },

  // 扫码识别设备
  scanEquipment() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        app.showToast('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult(result) {
    try {
      const data = JSON.parse(result)
      if (data.type === 'equipment' && data.id) {
        // 跳转到对应设备详情
        wx.redirectTo({
          url: `/pages/equipment/detail?id=${data.id}`
        })
      } else {
        app.showToast('无效的设备二维码')
      }
    } catch (error) {
      app.showToast(`扫码结果: ${result}`)
    }
  },

  // 设备检查
  checkEquipment() {
    wx.showModal({
      title: '设备检查',
      content: '确认要进行设备检查吗？',
      success: (res) => {
        if (res.confirm) {
          this.performEquipmentCheck()
        }
      }
    })
  },

  // 执行设备检查
  async performEquipmentCheck() {
    try {
      app.showLoading('检查中...')
      
      await app.request({
        url: `/api/equipment/${this.data.equipmentId}/check`,
        method: 'POST'
      })
      
      app.showToast('检查完成', 'success')
      this.loadEquipmentDetail(this.data.equipmentId)
      
    } catch (error) {
      console.error('设备检查失败:', error)
      app.showToast('检查失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 维护记录
  maintainEquipment() {
    wx.navigateTo({
      url: `/pages/equipment/maintenance?id=${this.data.equipmentId}`
    })
  },

  // 故障报告
  reportIssue() {
    wx.navigateTo({
      url: `/pages/equipment/report-issue?id=${this.data.equipmentId}`
    })
  },

  // 查看全部记录
  viewAllRecords() {
    wx.navigateTo({
      url: `/pages/equipment/maintenance-records?id=${this.data.equipmentId}`
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `设备详情 - ${this.data.equipment.name}`,
      path: `/pages/equipment/detail?id=${this.data.equipmentId}`,
      imageUrl: '/static/images/equipment-share.png'
    }
  }
})
