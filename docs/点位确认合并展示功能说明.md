# 点位确认合并展示功能说明

## 功能概述

点位确认功能新增了合并展示模式，可以将相同名称的点位合并显示，并支持对相同名称点位的批量操作。

## 功能特性

### 1. 合并展示
- **相同名称合并**：相同点位名称的所有明细会合并为一行显示
- **状态统计**：显示已确认数量/总数量，如 "2/5" 表示5个明细中有2个已确认
- **状态标识**：
  - 绿色：全部已确认
  - 橙色：部分已确认
  - 灰色：全部未确认
- **简洁展示**：只显示合并后的点位信息，不提供明细展开功能

### 2. 批量操作
- **批量确认**：可以对相同名称的所有点位明细进行批量确认
- **批量重命名**：可以对相同名称的所有点位明细进行批量重命名（仅限全部未确认）
- **智能限制**：已确认的点位不能重命名，确保数据完整性

### 3. 展示模式切换
- **默认合并展示**：新的默认展示模式，提高操作效率
- **详细展示**：可切换到原有的详细展示模式，查看每个明细
- **灵活切换**：两种模式可以随时切换

## 技术实现

### 后端实现

#### 1. 新增DTO类
```python
# 合并后的点位DTO
class MergedPointItemDTO(BaseModel):
    point_name: str              # 点位名称
    items: List[PointItemConfirmationDTO]  # 相同名称的所有点位明细
    total_count: int             # 总数量
    confirmed_count: int         # 已确认数量
    all_confirmed: bool          # 是否全部确认
    has_unconfirmed: bool        # 是否有未确认的
    can_rename: bool             # 是否可以重命名

# 按点位名称批量确认DTO
class BatchPointItemConfirmByNameDTO(BaseModel):
    project_quotation_id: int
    point_name: str
    confirmed: bool

# 按点位名称批量重命名DTO
class BatchPointItemRenameByNameDTO(BaseModel):
    project_quotation_id: int
    old_point_name: str
    new_point_name: str
```

#### 2. 新增API接口
```python
# 获取合并后的点位列表
GET /quotation/point-item-confirmation/project/{project_quotation_id}/merged

# 按点位名称批量确认
PUT /quotation/point-item-confirmation/batch-confirm-by-name

# 按点位名称批量重命名
PUT /quotation/point-item-confirmation/batch-rename-by-name
```

#### 3. 服务层方法
- `get_merged_point_items_by_quotation_id()`: 获取合并后的点位列表
- `batch_confirm_point_items_by_name()`: 按点位名称批量确认
- `batch_rename_point_items_by_name()`: 按点位名称批量重命名

### 前端实现

#### 1. 新增组件
- `MergedPointItemConfirmation.vue`: 合并展示组件
- `PointItemConfirmationWrapper.vue`: 包装组件，管理两种展示模式

#### 2. 主要功能
- 合并展示表格，简洁清晰
- 批量选择和批量操作
- 内联编辑重命名
- 确认状态管理
- 展示模式切换

## 使用说明

### 1. 打开点位确认对话框
- 默认显示合并展示模式
- 相同名称的点位会合并为一行

### 2. 查看点位状态
- 确认状态列显示：已确认数量/总数量
- 通过颜色标识快速识别确认状态

### 3. 批量确认操作
1. 选择要确认的点位（可多选）
2. 点击"批量确认"按钮
3. 确认操作后，该点位的所有明细都会被确认

### 4. 重命名操作
1. 点击点位行的"重命名"按钮
2. 输入新的点位名称
3. 按回车或点击保存
4. 该点位的所有明细都会被重命名

### 5. 切换展示模式
- 点击右上角的"切换到详细展示"按钮可切换到原有模式
- 在详细展示模式下，点击"切换到合并展示"返回

## 业务规则

### 1. 重命名限制
- 只有全部未确认的点位才能重命名
- 如果点位中有任何一个明细已确认，则不能重命名
- 重命名会影响该点位的所有明细

### 2. 确认规则
- 批量确认会确认该点位的所有未确认明细
- 已确认的明细不会重复确认
- 确认后的明细不能再修改

### 3. 数据一致性
- 所有操作都是事务性的，确保数据一致性
- 操作失败时会回滚，不会产生脏数据

## 优势

1. **界面简洁**：相同名称点位合并显示，界面更加简洁清晰
2. **批量操作**：支持批量确认和重命名，提高操作效率
3. **直观展示**：状态统计一目了然，快速了解确认进度
4. **灵活切换**：保留原有详细模式，满足不同需求
5. **数据安全**：严格的业务规则，确保数据完整性

## 兼容性

- 完全兼容现有的点位确认功能
- 不影响现有的数据结构和业务流程
- 可以随时切换回原有的详细展示模式
